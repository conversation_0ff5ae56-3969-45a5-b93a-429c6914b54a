const {GetObjectCommand, S3Client} = require('@aws-sdk/client-s3')
const {Upload} = require('@aws-sdk/lib-storage')
const unzipper = require('unzipper')
const {Readable} = require('node:stream')
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool()

const s3 = new S3Client({})

exports.handle = (e, ctx, cb) => {
  console.log('e', e)
  console.log('ctx', ctx)

  const tenantNo = 1
  const params = e.body
  if (!params) {
    return cb(null, {})
  }
  const bucket = process.env.S3_BUCKET
  console.log(bucket)

  let manageNos = null

  const files = []
  const filePath = `item-ancillary/${Common.dateToString(new Date())}-${Common.randomString(10)}/`
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: params.key,
  })
  return s3
    .send(command)
    .then(data => {
      const readableStream = Readable.fromWeb(data.Body?.transformToWebStream())
      return readableStream
        .pipe(unzipper.Parse())
        .on('entry', entry => {
          const fileName = entry.path
            .replace(/[^0-9a-zA-Z -/:-@\[-~]/g, '')
            .replace(/\[*\"*\s*\]*/g, '')
          const type = entry.type
          console.log(fileName)
          if (type === 'File') {
            const {name, ext} = Common.parseFileName(fileName)
            console.log(`name: ${name}`, `ext: ${ext}`)
            if (!name || !ext || !['jpg', 'jpeg', 'png', 'mp4'].includes(ext)) {
              entry.autodrain()
              return Promise.resolve()
            }

            // Save file to S3
            files.push({
              fileName,
              key: filePath + fileName,
            })

            // Get manage_no
            const tmpSplit = fileName.split('/')
            console.log(tmpSplit)
            if (tmpSplit && tmpSplit.length > 1) {
              manageNos = (manageNos || []).concat(tmpSplit[1] || [])
            }

            const upload = new Upload({
              client: s3,
              params: {
                Bucket: bucket,
                Key: filePath + fileName,
                Body: entry,
              },
            })
            return upload.done().catch(error => {
              console.log('error', error)
              return Promise.resolve()
            })
          }
          entry.autodrain()
          return Promise.resolve()
        })
        .promise()
    })
    .then(() => {
      return pool.rlsQuery(
        tenantNo,
        'SELECT * FROM "f_insert_item_ancillary_files"($1,$2);',
        [
          tenantNo,
          files.sort((a, b) => {
            if (a.fileName.endsWith('.mp4') && !b.fileName.endsWith('.mp4')) {
              return 1
            }
            if (b.fileName.endsWith('.mp4') && !a.fileName.endsWith('.mp4')) {
              return -1
            }
            return a.fileName < b.fileName ? -1 : 1
          }),
        ]
      )
    })
    .then(() => {
      return pool.rlsQuery(
        tenantNo,
        'SELECT * FROM "f_batch_link_item_ancillary_files"($1);',
        [manageNos]
      )
    })
    .then(() => {
      const videoFiles = files.filter(file => file.fileName.endsWith('.mp4'))
      console.log('videoFiles', videoFiles)
      return Promise.all(
        videoFiles.map(file => {
          return Common.invokeLambdaEventType(
            process.env.GET_FIRST_FRAME_MP4_LAMBDA_ARN,
            {
              key: file.key,
            },
            {maxRetries: 3}
          )
        })
      )
    })
    .then(result => {
      console.log(result)
      return cb(null, result)
    })
    .catch(error => {
      console.log(error)
      return Common.createErrorResponse(cb, error)
    })
}
