const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool();

exports.handle = function (e, ctx, cb) {
  const params = e.body;
  const header = e.headers;
  const authorizer = e.authorizer;
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      if (params.favorited) {
        return pool.query('SELECT * FROM "f_delete_favorite_stock"($1,$2);', [
          params.exhibition_item_no,
          authorizer.member_no,
        ]);
      } else {
        return pool.query(
          'SELECT * FROM "f_insert_favorite_stock"($1,$2,$3,$4);',
          [
            authorizer.tenant_no,
            authorizer.member_no,
            authorizer.user_no,
            params.exhibition_item_no,
          ]
        );
      }
    })
    .then(data => {
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
