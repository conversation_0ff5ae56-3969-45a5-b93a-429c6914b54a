const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)

const jwt = require('jsonwebtoken')

const pool = new PgPool()

/**
 * Verifies JWT token and extracts user information
 * @param token
 */
async function verifyToken(token) {
  try {
    // Remove 'Bearer ' prefix if present
    const cleanToken = token.replace(/^Bearer\s+/, '')

    // Decode token without verification first to get header
    const decoded = jwt.decode(cleanToken, {complete: true})
    if (!decoded) {
      throw new Error('Invalid token format')
    }

    // Verify token is from auction user pool
    const payload = decoded.payload
    if (payload.aud !== process.env.AUCTION_COGNITO_CLIENT_ID) {
      throw new Error('Token is not from auction user pool')
    }
    return payload
  } catch (error) {
    console.error('Token verification failed:', error)
    throw new Error('Invalid or expired token')
  }
}

/**
 * Extracts tenant information from Cognito user groups
 * @param tokenPayload
 */
function extractTenantFromToken(tokenPayload) {
  const groups = tokenPayload['cognito:groups'] || []

  for (const group of groups) {
    if (group.startsWith('tenant-id:')) {
      const tenantNo = group.split(':')[1]
      return parseInt(tenantNo, 10)
    }
  }

  throw new Error('No tenant information found in user groups')
}

/**
 * Gets member information from database
 * @param email
 * @param tenantNo
 */
async function getMemberFromDatabase(email, tenantNo) {
  const result = await pool.rlsQuery(
    tenantNo,
    'SELECT * FROM "f_get_member_info_by_email"($1,$2);',
    [email, tenantNo]
  )

  if (!result || result.length === 0) {
    throw new Error('Member not found in database')
  }

  return result[0]
}

/**
 * Logs the login attempt
 * @param tenantNo
 * @param member
 * @param email
 * @param sourceIp
 * @param userAgent
 */
async function logLogin(tenantNo, member, email, sourceIp, userAgent) {
  await pool.rlsQuery(
    tenantNo,
    'SELECT * FROM "f_insert_login"($1,$2,$3,$4,$5,$6,$7);',
    [
      tenantNo,
      member.member_no,
      member.user_no,
      email,
      email,
      sourceIp,
      userAgent,
    ]
  )
  console.log('Login logged successfully')
}

/**
 * Main login history logging logic
 * @param e
 */
async function logLoginHistory(e) {
  const params = JSON.parse(e.body || '{}')
  const base = new Base(pool, params.languageCode || 'ja')

  await base.startRequest(e)

  const authHeader = e.headers.Authorization || e.headers.authorization
  if (!authHeader) {
    throw {
      status: 401,
      name: 'Missing Authorization',
      message: '認証トークンが必要です。',
    }
  }

  try {
    // 1. Verify token and extract user info
    const tokenPayload = await verifyToken(authHeader)
    const email = tokenPayload.email

    if (!email) {
      throw new Error('Email not found in token')
    }

    // 2. Extract tenant information from token
    const tenantNo = extractTenantFromToken(tokenPayload)

    // 3. Get member info from database
    const member = await getMemberFromDatabase(email, tenantNo)

    // 4. Log the login
    await logLogin(
      tenantNo,
      member,
      email,
      e.requestContext?.identity?.sourceIp || 'unknown',
      e.requestContext?.identity?.userAgent || 'unknown'
    )

    return {
      success: true,
      message: 'ログイン履歴が記録されました。',
    }
  } catch (error) {
    console.error('Login history logging failed:', error)
    throw {
      status: error.status || 500,
      name: error.name || 'Login History Error',
      message: error.message || 'ログイン履歴の記録に失敗しました。',
    }
  }
}

/**
 * Lambda handler function
 * @param e
 * @param ctx
 * @param cb
 */
exports.handle = (e, ctx, cb) => {
  ctx.callbackWaitsForEmptyEventLoop = false
  console.log('login-history-logging:', e)

  const base = new Base(pool, 'ja')

  logLoginHistory(e)
    .then(result => base.createSuccessResponse(cb, result))
    .catch(error => {
      console.error('Login history logging failed:', error)
      const errorResponse = {
        status: error.status || 500,
        name: error.name || 'Login History Error',
        message: error.message || 'ログイン履歴の記録に失敗しました。',
      }
      return base.createErrorResponse(cb, errorResponse)
    })
}
