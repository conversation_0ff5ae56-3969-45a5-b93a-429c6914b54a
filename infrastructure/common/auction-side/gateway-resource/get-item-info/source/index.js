const Base = require(process.env.COMMON_LAYER_PATH + 'base.js');
const PgPool = require(process.env.COMMON_LAYER_PATH + 'pg-pool.js');
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = e.body;
  const header = e.headers;
  const authorizer = e.authorizer;
  const base = new Base(pool, params.languageCode);
  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => base.checkOrigin(header.origin || header.Origin))
    .then(tenant =>
      pool.query('SELECT * FROM "f_get_item_info"($1,$2,$3,$4);', [
        params.exhibitionItemNo,
        tenant.tenant_no,
        base.language,
        authorizer.member_no,
      ])
    )
    .then(result => {
      const data = result.length > 0 ? result[0] : null;
      return base.createSuccessResponse(cb, data);
    })
    .catch(error => base.createErrorResponse(cb, error));
};
