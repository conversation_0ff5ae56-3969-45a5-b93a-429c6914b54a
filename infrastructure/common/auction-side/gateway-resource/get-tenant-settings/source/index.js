const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

exports.handle = (e, ctx, cb) => {
  const params = e.body || {}
  const base = new Base(pool, params.languageCode)
  const tenantId = Common.extractTenantId(e)

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      const sqlParams = [tenantId]
      return pool.rlsQuery(
        tenantId,
        'SELECT * FROM "f_get_tenant_settings"($1);',
        sqlParams
      )
    })
    .then(data => {
      const tenantSettings = data.length > 0 ? data[0] : {}
      return base.createSuccessResponse(cb, tenantSettings)
    })
    .catch(error => base.createErrorResponse(cb, error))
}
