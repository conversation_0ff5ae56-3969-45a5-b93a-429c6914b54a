resource "aws_iam_role_policy" "cognito_register_member_policy" {
  name = "${var.project_name}-${var.environment}-cognito-register-member-policy"

  role   = module.gateway-resource.lambda_iam_role_id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "cognito-idp:AdminCreateUser",
          "cognito-idp:AdminSetUserPassword",
          "cognito-idp:AdminAddUserToGroup",
          "cognito-idp:AdminGetUser",
          "cognito-idp:AdminUpdateUserAttributes"
        ],
        Effect   = "Allow",
        Resource = "arn:aws:cognito-idp:${data.aws_region.current.id}:${data.aws_caller_identity.current.account_id}:userpool/${var.cognito_user_pool_id}"
      }
    ]
  })
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
