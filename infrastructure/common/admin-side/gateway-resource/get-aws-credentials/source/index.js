const {fromTemporaryCredentials} = require('@aws-sdk/credential-providers')
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

const getUploadRoleByType = type => {
  switch (type) {
    case process.env.NOTICE_FILE_UPLOAD:
      return process.env.UPLOAD_NOTICE_FILE_ROLE_ARN
    case process.env.CONSTANT_FILE_UPLOAD:
      return process.env.UPLOAD_CONSTANT_FILE_ROLE_ARN
    case process.env.PUBLIC_FILE_UPLOAD:
      return process.env.UPLOAD_PUBLIC_FILE_ROLE_ARN
    case process.env.EXHIBITION_EMAIL_FILE_UPLOAD:
      return process.env.UPLOAD_EXHIBITION_EMAIL_FILE_ROLE_ARN
    case process.env.MEMBER_REQUEST_UPLOAD:
      return process.env.UPLOAD_MEMBER_REQUEST_FILE_ROLE_ARN
    case process.env.CSV_FILE_UPLOAD:
      return process.env.UPLOAD_CSV_FILE_ROLE_ARN
    case process.env.NOTICE_EMAIL_FILE_UPLOAD:
      return process.env.UPLOAD_NOTICE_EMAIL_FILE_ROLE_ARN
    case process.env.ITEM_ANCILLARY_UPLOAD:
      return process.env.UPLOAD_ITEM_ANCILLARY_FILE_ROLE_ARN
    case process.env.STATIC_PAGE_FILE_UPLOAD:
      return process.env.UPLOAD_STATIC_PAGE_FILE_ROLE_ARN
    default:
      return ''
  }
}

exports.handle = (e, ctx, cb) => {
  console.log(`e.authorizer = ${JSON.stringify(e.authorizer)}`)
  const params = Base.parseRequestBody(e.body)
  console.log(`params = ${JSON.stringify(params)}`)

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() => Validator.validation(params))
    .then(() => {
      const getCredentials = fromTemporaryCredentials({
        params: {
          DurationSeconds: process.env.AWS_CREDENTIALS_DURATION_SECONDS,
          RoleArn: getUploadRoleByType(params.type),
        },
      })
      return getCredentials()
    })
    .then(credentials => {
      console.log('credentials', credentials)
      const response = {
        bucket: process.env.S3_BUCKET,
        region: process.env.AWS_REGION,
        prefix_key: `${params.type}/${Common.dateToString(new Date())}-${Base.randomString(10)}`,
        credentials,
      }
      // Return Base.createSuccessResponse(cb, response, deflate)
      return Base.createSuccessResponse(cb, response)
    })
    .catch(error => {
      console.log(error)
      return Base.createErrorResponse(cb, error)
    })
}
