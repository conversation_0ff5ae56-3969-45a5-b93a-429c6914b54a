const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = function (e, ctx, cb) {
  const Rules = require('./validation-rules');
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log(params);

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      let validateRule = {};
      validateRule = Object.assign(validateRule, Rules.updateConstantRule);

      return Validator.validation(params, validateRule).then(() => {
        // Custom validation for display_area based on payload format
        if (params.window_id === 'item_detail') {
          if (Array.isArray(params.field_no) && params.field_no.length > 0) {
            if (typeof params.field_no[0] === 'object' && params.field_no[0].hasOwnProperty('no')) {
              // New format: check that all field objects have display_area
              const missingDisplayArea = params.field_no.some(field => !field.display_area || field.display_area.trim() === '');
              if (missingDisplayArea) {
                return Promise.reject({
                  status: 400,
                  errors: {
                    field_no: 'Field numbers are required'
                  }
                });
              }
            }
          }
        }

        // Each object contains: {no: field_number, label: field_name, display_area: display_area}
        let field_no_data;

        if (Array.isArray(params.field_no) && params.field_no.length > 0) {
          if (typeof params.field_no[0] === 'object' && params.field_no[0].hasOwnProperty('no')) {
            // New format: pass the entire field objects array as JSONB
            field_no_data = params.field_no;
          } else {
            // Old format: convert array of numbers to new format with empty display_area
            field_no_data = params.field_no.map(fieldNo => ({
              no: fieldNo,
              label: '', // Will be populated by migration or left empty for old data
              display_area: params.display_area || ''
            }));
          }
        } else {
          // Fallback
          field_no_data = [];
        }

        const sqlParams = [
          params.field_mapping_no,
          tenantNo,
          params.window_id,
          params.language_code,
          JSON.stringify(field_no_data),
        ];
        console.log('🔈 log of item : ', sqlParams)
        return pool.rlsQuery(
          tenantNo,
          Define.QUERY.REGIST_UPDATE_DISPLAY_FIELD_LIST_FUNCTION,
          sqlParams
        );
      });
    })
    .then(result => {
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      if (error.status === 400 && error.errors.chilConstants) {
        const chilConstantsError = error.errors.chilConstants;
        delete error.errors.chilConstants;
        Object.keys(chilConstantsError).map(chiErr => {
          Object.values(chilConstantsError[chiErr]).map(ele => {
            const key = `${params.chilConstants[chiErr].languageCode}${Base.randomString(3)}`;
            error.errors[key] = Common.format(ele, [
              params.chilConstants[chiErr].language
                ? `${params.chilConstants[chiErr].language}の`
                : '',
            ]);
          });
        });
      }
      return Base.createErrorResponse(cb, error);
    });
};
