const {
  HeadObjectCommand,
  GetObjectCommand,
  S3Client,
} = require('@aws-sdk/client-s3')
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner')
const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body)
  const client = new S3Client({})
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() => {
      return Validator.validation(params)
    })
    .then(() => {
      const command = new HeadObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: params.key,
      })
      return client.send(command)
    })
    .then(data => {
      console.log('data', data)
      // get pre-signed url
      const getParams = {
        Bucket: process.env.S3_BUCKET,
        Key: params.key,
      }
      if (params.file_name) {
        getParams.ResponseContentDisposition = `attachment; filename=${encodeURIComponent(params.file_name)}`
      }
      if (params.content_type) {
        getParams.ResponseContentType = params.content_type
      }
      const expiresIn = Number.parseInt(process.env.SIGNED_URL_EXPIRES, 10)
      const command = new GetObjectCommand(getParams)
      return getSignedUrl(client, command, {expiresIn})
    })
    .then(result => {
      console.log('result', result)
      return Base.createSuccessResponse(cb, result)
    })
    .catch(error => {
      if (error.code === 'Forbidden') {
        const response = {
          statusCode: 400,
          errors: {
            key: Define.MESSAGE.E700002,
          },
        }
        return Base.createErrorResponse(cb, response)
      }
      return Base.createErrorResponse(cb, error)
    })
}
