module.exports = {
  updateEmailRule: {
    exhibitionName: {
      REQUIRED_CHECK: true,
      REQUIRED_ERROR_MESSAGE: 'E000338',
    },
    emailReq: {
      LIST_CHECK: true,
      ELEMENT_RULES: {
        classification: {
          REQUIRED_CHECK: true,
          REQUIRED_ERROR_MESSAGE: 'E000339',
        },
        sendFlag: {
          REQUIRED_CHECK: true,
          REQUIRED_ERROR_MESSAGE: 'E000340',
          CONTAIN_CHECK_LIST: [0, 1],
          CONTAIN_CHECK_ERROR_MESSAGE: 'E000341',
        },
        compareWithNow: {
          PATTERN_CHECK: true,
          PATTERN: RegExp('^[0-9]{1,}$'),
          PATTERN_ERROR_MESSAGE: 'E000343',
        },
        dateTimeVali: {
          PATTERN_CHECK: true,
          PATTERN: RegExp('^[0-9]{1,}$'),
          PATTERN_ERROR_MESSAGE: 'E000342',
        },
        dateTimeCheck: {
          REQUIRED_CHECK: true,
          REQUIRED_ERROR_MESSAGE: 'E000344',
        },
        unselectOneLaterYearSendDate: {
          PATTERN_CHECK: true,
          PATTERN: /^[0-9]{1,}$/,
          PATTERN_ERROR_MESSAGE: 'E000366',
        },
      },
      LIST_CHECK_ERROR_MESSAGE: 'E000331',
    },
    emailLanguageReq: {
      LIST_CHECK: true,
      ELEMENT_RULES: {
        titleVali: {
          REQUIRED_CHECK: true,
          REQUIRED_ERROR_MESSAGE: 'E000329',
        },
        title: {
          MAX_LENGTH_CHECK: true,
          MAX_LENGTH: 78,
          MAX_LENGTH_ERROR_MESSAGE: 'E000336',
        },
      },
      LIST_CHECK_ERROR_MESSAGE: 'E000331',
    },
  },
};
