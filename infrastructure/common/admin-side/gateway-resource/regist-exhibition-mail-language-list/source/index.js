const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = (e, ctx, cb) => {
  const Rules = require('./validation-rules');
  const params = Base.parseRequestBody(e.body);
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log(params);
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      let validateRule = {};
      if (params.emailReq) {
        params.emailReq.map(chil => {
          const now = new Date(
            Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000
          );
          if (
            chil.dateTimeValiFlag &&
            ((chil.date && chil.time) || (!chil.date && !chil.time))
          ) {
            if (chil.date && chil.time) {
              chil.dateTime = `${chil.date} ${chil.time}`;
              chil.dateTimeCheck = 1;
              chil.compareWithNow =
                chil.dateTimeChangeFlag === false && chil.status === 1
                  ? 1
                  : Date.parse(chil.dateTime) - Date.parse(now);
              // 1年後以降の日付を選択していないか確認
              // 1年後の日付を取得
              const today = new Date(Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000);
              const maxDate = new Date(today);
              maxDate.setDate(today.getDate() + 364);
              maxDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得
              const sendDate = new Date(
                `${chil.date}T00:00:00`
              );
              const sendDateDiff = maxDate - sendDate;
              chil.unselectOneLaterYearSendDate = sendDateDiff;
            }
            if (!chil.date && !chil.time && chil.sendFlag === 0) {
              chil.dateTimeCheck = 1;
            }
          } else {
            chil.dateTimeVali = -1;
            chil.compareWithNow = 1;
            chil.dateTimeCheck = 1;
            chil.unselectOneLaterYearSendDate = 1;
          }
        });
      }
      if (params.emailLanguageReq) {
        params.emailLanguageReq.map(chil => {
          if (chil.sendFlag === 0 || chil.title) {
            chil.titleVali = 'true';
          }
        });
      }
      validateRule = Object.assign(validateRule, Rules.updateEmailRule);
      return Validator.validation(params, validateRule)
        .then(() => {
          const sqlParams = [
            Base.extractTenantId(e),
            Base.extractAdminNo(e),
            params.emailReq,
          ];
          console.log(sqlParams);
          return pool.rlsQuery(Base.extractTenantId(e),
            Define.QUERY.REGIST_UPDATE_EXHIBITION_EMAIL_FUNCTION,
            sqlParams
          );
        })
        .then(() => {
          const sqlParams = [
            Base.extractTenantId(e),
            Base.extractAdminNo(e),
            params.emailLanguageReq,
          ];
          console.log(sqlParams);
          return pool.rlsQuery(Base.extractTenantId(e),
            Define.QUERY.REGIST_UPDATE_EXHIBITION_EMAIL_LANGUAGE_FUNCTION,
            sqlParams
          );
        });
    })
    .then(result => {
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      console.log(error);
      if (error.status === 400 && error.errors.emailLanguageReq) {
        const chilEmailsError = error.errors.emailLanguageReq;
        delete error.errors.emailLanguageReq;
        Object.keys(chilEmailsError).map(chiErr => {
          Object.values(chilEmailsError[chiErr]).map(ele => {
            const key = `${params.emailLanguageReq[chiErr].languageCode}${Base.randomString(3)}`;
            error.errors[key] = Common.format(ele, [
              params.emailLanguageReq[chiErr].target,
              params.emailLanguageReq[chiErr].language
                ? `${params.emailLanguageReq[chiErr].language}の`
                : '',
            ]);
          });
        });
      }
      if (error.status === 400 && error.errors.emailReq) {
        const chilEmailsError = error.errors.emailReq;
        delete error.errors.emailReq;
        Object.keys(chilEmailsError).map(chiErr => {
          Object.values(chilEmailsError[chiErr]).map(ele => {
            const key = `${Base.randomString(3)}`;
            error.errors[key] = Common.format(ele, [
              params.emailReq[chiErr].target,
            ]);
          });
        });
      }
      return Base.createErrorResponse(cb, error);
    });
};
