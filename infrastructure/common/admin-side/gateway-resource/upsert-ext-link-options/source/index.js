const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('upsert_ext_link_options');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const sqlParams = [
        Base.extractTenantId(e),
        params.ext_link_no,
        params.ext_link_options,
        Base.extractAdminNo(e),
      ];
      return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.UPSERT_EXT_LINK_OPTIONS_FUNCTION, sqlParams);
    })
    .then(extLinkOptions => {
      console.log(`extLinkOptions = ${JSON.stringify(extLinkOptions)}`);
      return Base.createSuccessResponse(cb, extLinkOptions);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
