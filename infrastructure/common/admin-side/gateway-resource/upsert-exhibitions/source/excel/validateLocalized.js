const {cloneDeep} = require('lodash')
const importRules = require('./xlsx-import-rules')
const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)

const validateLocalized = ({pool, tenantNo, params}) => {
  console.log('validateLocalized: ', params)
  const {lang, data: importedLots, field} = params
  const commonRules = cloneDeep(importRules)

  return Promise.resolve()
    .then(() => {
      console.log('GET CONSTANT')
      console.log('field:', field)
      console.log('field.constant_key_strings:', field?.constant_key_strings)

      // Ensure constant_key_strings is an array
      const constantKeys =
        field && Array.isArray(field.constant_key_strings)
          ? field.constant_key_strings
          : []

      const sqlParams = [[...constantKeys, 'LANGUAGE_CODE'], tenantNo, lang]
      return pool
        .rlsQuery(tenantNo, Define.QUERY.GET_CONSTANTS_BY_KEYS, sqlParams)
        .then(constants => {
          return Promise.resolve(constants)
        })
    })
    .then(constants => {
      console.log('PREPARE ITEMS INFO')
      // Prepare members
      const items = []
      for (const csvLine of importedLots) {
        const {
          manage_no,
          recommend_flag,
          lowest_bid_price,
          lowest_bid_accept_price,
          quantity,
          lowest_bid_quantity,
          lowest_bid_accept_quantity,
          ...rest
        } = csvLine
        items.push({
          manage_no,
          recommend_flag,
          lowest_bid_price,
          lowest_bid_accept_price,
          quantity,
          lowest_bid_quantity,
          lowest_bid_accept_quantity,
          free_field: {
            ...rest,
          },
        })
      }
      console.log(`items: ${items.length}`)
      return Promise.resolve({
        items,
        constants,
      })
    })
    .then(({items, constants}) => {
      console.log('VALIDATE ALL ITEMS')
      return new Promise((resolve, reject) => {
        Common.splitAll(
          (item, index) => {
            // 初期化
            // let itemDefaultEndDateTime = null

            return Promise.resolve()
              .then(() => {
                console.log('GET ORIGINAL ITEM INFO')

                console.log(`item.manage_no: ${item.manage_no}`)
                if (!Validator.checkRequired(item.manage_no)) {
                  return Promise.resolve()
                }

                // Get item's current information by language code
                const sql_params = [
                  tenantNo,
                  params.exhibition_no,
                  item.manage_no,
                  lang,
                ]
                return pool
                  .rlsQuery(
                    tenantNo,
                    Define.QUERY.GET_ITEM_BY_MANAGE_NO,
                    sql_params
                  )
                  .then(result => {
                    console.log('result: ', result)
                    if (
                      typeof result === 'undefined' ||
                      result === null ||
                      result.length === 0
                    ) {
                      return Promise.resolve(null)
                    }
                    return Promise.resolve(result[0])
                  })
                  .catch(() => {
                    const err = {
                      index: index + 1,
                      manage_no: 'E100404',
                    }
                    return Promise.reject(err)
                  })
              })
              .then(orig_item => {
                console.log('CHECK ITEM STATUS')
                // Check manage_no duplicate inside list
                const manageNoList = items.map(item => item.manage_no)
                if (
                  manageNoList.filter((x, i, self) => self.indexOf(x) !== i)
                    .length > 0
                ) {
                  const err = {
                    index: index + 1,
                    manage_no: 'E100414',
                  }
                  return Promise.reject(err)
                }

                // Check status
                if (orig_item) {
                  // 直接成約されている商品(t_item.status = 3で)の場合はエラーにする
                  // 「成約済みのため出品できません」
                  if (orig_item.status === 3) {
                    const err = {
                      index: index + 1,
                      message_no: 'E100405',
                    }
                    return Promise.reject(err)
                  }
                  if (
                    orig_item.status === 2 &&
                    params.exhibition_no &&
                    String(orig_item.exhibition_no) ===
                      String(params.exhibition_no)
                  ) {
                    // 既に別の入札会に出品されている商品(t_item.status = 2で別の入札会に出品中
                    // ※選択中の入札会に出品中の場合はエラーにしない)の場合はエラーメッセージを表示する
                    // 「すでに別の入札会に出品されているため出品できません」
                    const err = {
                      index: index + 1,
                      status: 'E100406',
                    }
                    return Promise.reject(err)
                  }

                  // 入札が既にある場合は最低入札価格、数量などを変更させない
                  console.log('bid_count: ', orig_item.bid_count)
                  if (orig_item.bid_count > 0) {
                    const errs = {}
                    for (const key of [
                      'lowest_bid_price',
                      'lowest_bid_accept_price',
                      'lowest_bid_quantity',
                      'lowest_bid_accept_quantity',
                      'quantity',
                    ]) {
                      console.log(`orig_item.${key}: `, orig_item[key])
                      console.log(`item.${key}: `, item[key])
                      if (orig_item[key] !== item[key]) {
                        const columnName =
                          Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME[key]
                        const msg = Common.format(Define.MESSAGE.E100409, [
                          columnName,
                        ])
                        errs[key] = msg
                      }
                    }
                    if (Object.keys(errs).length > 0) {
                      return Promise.reject({index: index + 1, ...errs})
                    }
                  }

                  // 次に商品ごとの終了時間を確認するため
                  // itemDefaultEndDateTime = orig_item.default_end_datetime
                }

                return Promise.resolve()
              })
              .then(() => {
                console.log('Validate common rules')
                const checkParam = Object.assign({}, item, item.free_field)

                // Check valid price
                checkParam.lowestBidAcceptPriceErr = 0
                if (
                  !Common.isEmpty(checkParam.lowest_bid_accept_price) &&
                  !Common.isEmpty(checkParam.lowest_bid_price) &&
                  checkParam.lowest_bid_accept_price <
                    checkParam.lowest_bid_price
                ) {
                  checkParam.lowestBidAcceptPriceErr = -1
                }

                const validateCommonResult = Validator.checkRules(
                  checkParam,
                  commonRules
                )
                console.log(
                  'checkCommonRules -> validateCommonResult : ',
                  validateCommonResult
                )

                // Check for localized fields
                console.log('Validation for localized item fields')
                console.log('field: ', field)
                if (
                  !field ||
                  !field.field_list ||
                  field.field_list.length === 0
                ) {
                  // No item fields defined
                  return Promise.resolve()
                }
                // Check if the item_fields match the fields in the database
                const {field_list} = field
                const localizedRules = (field_list || []).reduce((acc, x) => {
                  acc[x.physical_name] = {
                    // Check required
                    REQUIRED_CHECK: x.required_flag === 1,
                    REQUIRED_ERROR_MESSAGE: Common.format(
                      Define.MESSAGE.E020000,
                      [x.logical_name]
                    ),
                    // Check max length
                    MAX_LENGTH_CHECK: x.max_length > 0,
                    MAX_LENGTH: x.max_length,
                    MAX_LENGTH_ERROR_MESSAGE: Common.format(
                      Define.MESSAGE.E020002,
                      [x.logical_name, x.max_length]
                    ),
                    // Check pattern
                    PATTERN_CHECK:
                      x.data_type === 'reg' && x.regular_expressions,
                    PATTERN: x.regular_expressions
                      ? new RegExp(x.regular_expressions)
                      : null,
                    PATTERN_ERROR_MESSAGE: Common.format(
                      Define.MESSAGE.E020001,
                      [x.logical_name]
                    ),
                    // Contains check
                    CONTAIN_CHECK:
                      x.input_type === 'pulldown' ||
                      x.input_type === 'checkbox',
                    CONTAIN_CHECK_LIST:
                      constants
                        ?.filter(
                          c =>
                            x.input_data_list?.key_string &&
                            c.key_string === x.input_data_list?.key_string
                        )
                        ?.map(t => t.value2) || [], // Text value, not key
                    CONTAIN_CHECK_ERROR_MESSAGE: Common.format(
                      Define.MESSAGE.E020001,
                      [x.logical_name]
                    ),
                  }
                  return acc
                }, {})
                console.log('localizedRules: ', localizedRules)
                const validateLocalizedResult = Validator.checkRules(
                  checkParam,
                  localizedRules
                )
                console.log(
                  'validateLocalizedResult: ',
                  validateLocalizedResult
                )

                const validateResult = {
                  ...validateCommonResult,
                  ...validateLocalizedResult,
                }
                // Validation Fail
                if (Object.keys(validateResult).length > 0) {
                  validateResult.index = index + 1
                  return Promise.resolve(validateResult)
                }

                // Correct data - Convert text to numbers
                item.recommend_flag = item.recommend_flag === 'あり' ? 1 : 0
                item.quantity = 1
                item.lowest_bid_quantity = 1
                item.lowest_bid_accept_quantity = 1
                if (
                  item.lowest_bid_price !== undefined &&
                  item.lowest_bid_price !== null &&
                  item.lowest_bid_price !== ''
                ) {
                  item.lowest_bid_price = Number(item.lowest_bid_price)
                }
                if (
                  item.lowest_bid_accept_price !== undefined &&
                  item.lowest_bid_accept_price !== null &&
                  item.lowest_bid_accept_price !== ''
                ) {
                  item.lowest_bid_accept_price = Number(
                    item.lowest_bid_accept_price
                  )
                }

                // Convert localized field values to numbers
                for (const key of Object.keys(item.free_field)) {
                  const fieldItem = field_list.find(
                    x => x.physical_name === key
                  )
                  if (
                    fieldItem &&
                    (fieldItem.input_type === 'pulldown' ||
                      fieldItem.input_type === 'checkbox') &&
                    item.free_field[key] !== undefined &&
                    item.free_field[key] !== null &&
                    item.free_field[key] !== ''
                  ) {
                    const cz = constants?.find(
                      c =>
                        fieldItem.input_data_list?.key_string &&
                        c.key_string ===
                          fieldItem.input_data_list?.key_string &&
                        c.value2 === item.free_field[key]
                    )
                    item.free_field[key] = cz ? cz.value1 : null
                  }
                }

                return Promise.resolve()
              })
              .catch(error => {
                console.log('error: ', error)
                return Promise.resolve(error)
              })
          },
          items.map((item, index) => {
            return [item, index]
          }),
          100,
          1
        ).then(results => {
          console.log('results = ', results)
          const resErrors = {}
          const errors = results.filter(res => {
            return (
              typeof res !== 'undefined' &&
              res !== null &&
              Object.keys(res).length > 0
            )
          })
          console.log(`errors: ${JSON.stringify(errors)}`)

          if (errors !== null && errors.length > 0) {
            for (const er of errors) {
              console.log('log:  er: ', er)
              const errMgs = Validator.convertErrorCodeToErrorMessage(er)
              delete errMgs.index // Remove index key

              for (const key of Object.keys(errMgs)) {
                console.log('log:  errMgs -> key: ', key)
                let columnName1 = ''
                let columnName2 = ''

                if (key !== 'index') {
                  let formattedMessage = ''
                  switch (key) {
                    case 'lowestBidAcceptPriceErr':
                      columnName1 =
                        Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME
                          .lowest_bid_price
                      columnName2 =
                        Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME
                          .lowest_bid_accept_price
                      formattedMessage = Common.format(errMgs[key], [
                        columnName1,
                        columnName2,
                      ])
                      break

                    default:
                      // eslint-disable-next-line no-case-declarations
                      const columnName =
                        Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME[key]
                      formattedMessage = Common.format(errMgs[key], [
                        columnName,
                      ])
                      break
                  }

                  // Add formatted message with row index
                  const langTxt =
                    constants?.find(
                      c => c.key_string === 'LANGUAGE_CODE' && c.value1 === lang
                    )?.value2 || lang
                  resErrors[`${lang}_${key}_${er.index}`] =
                    `[${langTxt}][${er.index}行目]${formattedMessage}`
                }
              }
            }
          }

          if (resErrors && Object.keys(resErrors).length > 0) {
            return reject(resErrors)
          }
          // Return converted data after successful validation
          return resolve(items)
        })
      })
    })
}

module.exports = validateLocalized
