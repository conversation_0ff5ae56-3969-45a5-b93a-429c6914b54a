module.exports = {
  manage_no: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
  },
  recommend_flag: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    CONTAIN_CHECK: true,
    CONTAIN_CHECK_LIST: ['あり', ''],
    CONTAIN_CHECK_ERROR_MESSAGE: 'E100401',
  },
  lowest_bid_price: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E100401',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E100412',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 8,
    MAX_LENGTH_ERROR_MESSAGE: 'E000256',
  },
  lowest_bid_accept_price: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E100401',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E100412',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 8,
    MAX_LENGTH_ERROR_MESSAGE: 'E000258',
  },
  lowestBidAcceptPriceErr: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100413',
  },
}
