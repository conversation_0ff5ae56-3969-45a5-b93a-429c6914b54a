const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = function (e, ctx, cb) {
  const Rules = require('./validation-rules');
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log(params);

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      let validateRule = {};
      validateRule = Object.assign(validateRule, Rules.updateConstantRule);

      return Validator.validation(params, validateRule).then(() => {
        const sqlParams = [
          tenantNo,
          params.external_system_no,
          params.target_object,
          JSON.stringify(params.external_field_mappings),
          JSON.stringify(params.delete_external_field_mappings),
        ];
        return pool.rlsQuery(
          tenantNo,
          Define.QUERY.REGIST_UPDATE_EXTERNAL_FIELD_MAPPING_LIST_FUNCTION,
          sqlParams
        );
      });
    })
    .then(result => {
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      if (error.status === 400 && error.errors.external_field_mappings) {
        const chilFieldMappingError = error.errors.external_field_mappings;
        delete error.errors.external_field_mappings;
        Object.keys(chilFieldMappingError).map(chiErr => {
          const arrayIndex = parseInt(chiErr); // 文字列を数値に変換
          const errorContext = chilFieldMappingError[chiErr];
          Object.values(errorContext).map(ele => {
            const key = `${arrayIndex}${Base.randomString(3)}`;
            const mappingItem = params.external_field_mappings[arrayIndex];
            //
            if(errorContext.external_system_item_key) {
              error.errors[key] = Common.format(ele, [
                mappingItem.auction_field_name !== '' ? mappingItem.auction_field_name + 'の' : ''
              ]);
            } else if (errorContext.field_localized_no) {
              error.errors[key] = Common.format(ele, [
                mappingItem.external_system_item_key !== '' ? mappingItem.external_system_item_key + 'の' : ''
              ]);
            }
          });
        });
      }
      // 削除の場合は項目名等関係なく、まとめてメッセージ表示
      if (error.status === 400 && error.errors.delete_external_field_mappings) {
        const chilDeleteFieldMappingError = error.errors.delete_external_field_mappings;
        delete error.errors.delete_external_field_mappings;

        const errorKeys = Object.keys(chilDeleteFieldMappingError);
        if (errorKeys.length > 0) {
          const firstErrorKey = errorKeys[0];
          const arrayIndex = parseInt(firstErrorKey); // 文字列を数値に変換
          const errorContext = chilDeleteFieldMappingError[firstErrorKey];
          const errorValues = Object.values(errorContext);
          if (errorValues.length > 0) {
            const key = `${arrayIndex}${Base.randomString(3)}`;
            error.errors[key] = Common.format(errorValues[0], []);
          }
        }
      }
      return Base.createErrorResponse(cb, error);
    });
};
