# IAM role for Cognito SMS
resource "aws_iam_role" "cognito_sns_role" {
  name = "${var.project_name}-${var.environment}-cognito-sns-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "cognito-idp.amazonaws.com"
        }
      },
    ]
  })
}

# Attach policy that allows sending SMS via SNS
resource "aws_iam_role_policy" "cognito_sns_policy" {
  name = "${var.project_name}-${var.environment}-cognito-sns-policy"
  role = aws_iam_role.cognito_sns_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "sns:Publish"
        ]
        Effect = "Allow"
        Resource = "*"
      },
    ]
  })
}
