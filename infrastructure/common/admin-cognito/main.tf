resource "aws_cognito_user_pool" "user_pool" {
  name = "${var.project_name}-${var.environment}-admin-side"

  # Allow users to sign in with email instead of username
  username_attributes = ["email"]
  auto_verified_attributes = ["email"]

  # Password policy
  password_policy {
    minimum_length                    = var.password_policy.minimum_length
    require_lowercase                 = var.password_policy.require_lowercase
    require_numbers                   = var.password_policy.require_numbers
    require_symbols                   = var.password_policy.require_symbols
    require_uppercase                 = var.password_policy.require_uppercase
    temporary_password_validity_days  = var.password_policy.temporary_password_validity_days
  }

  # Custom attributes
  schema {
    name                     = "admin_no"
    attribute_data_type      = "Number"
    mutable                  = true
    required                 = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                     = "member_no"
    attribute_data_type      = "Number"
    mutable                  = true
    required                 = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                     = "admin_language_code"
    attribute_data_type      = "String"
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      max_length = "2"
    }
  }
  # 権限ID(10:管理者,20:運用担当者,30:一般)
  schema {
    name                     = "role_id"
    attribute_data_type      = "String"
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      max_length = "2"
    }
  }
  schema {
    name                     = "admin_name"
    attribute_data_type      = "String"
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      max_length = "50"
    }
  }

  # Email verification configuration
  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
    email_subject = "[${var.project_name}] 検証コード"
    email_message = "検証コードは「{####}」です。"
  }

  # MFA is set to OFF - no email verification on login
  mfa_configuration = "OFF"

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }
}

resource "aws_cognito_user_group" "tenant_groups" {
  for_each = toset(var.tenant_ids)
  name        = "tenant-id:${each.value}"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  description = each.value == "0" || each.value == 0 ? "システム管理者用グループ" : "テナント${each.value}用グループ"
}

resource "aws_cognito_user_pool_client" "clients" {
  for_each = { for idx, client in var.app_clients : client.name => client }

  name                   = each.value.name
  user_pool_id           = aws_cognito_user_pool.user_pool.id
  generate_secret        = each.value.generate_secret
  refresh_token_validity = each.value.refresh_token_validity
  access_token_validity  = each.value.access_token_validity
  id_token_validity      = each.value.id_token_validity

  callback_urls          = each.value.callback_urls
  logout_urls            = each.value.logout_urls

  allowed_oauth_flows    = each.value.allowed_oauth_flows
  allowed_oauth_scopes   = each.value.allowed_oauth_scopes

  # Security setting to prevent user enumeration attacks
  prevent_user_existence_errors = "ENABLED"

  # Authentication flows allowed for this client application
  explicit_auth_flows = [
    "ALLOW_REFRESH_TOKEN_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_ADMIN_USER_PASSWORD_AUTH"
  ]
}

resource "aws_cognito_user_pool_domain" "cognito-domain" {
  domain       = var.domain_prefix
  user_pool_id = aws_cognito_user_pool.user_pool.id
}
