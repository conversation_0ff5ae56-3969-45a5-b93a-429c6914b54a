output "user_pool_id" {
  value       = aws_cognito_user_pool.user_pool.id
  description = "ID of the Cognito User Pool"
}

output "user_pool_arn" {
  value       = aws_cognito_user_pool.user_pool.arn
  description = "ARN of the Cognito User Pool"
}

output "domain" {
  value       = aws_cognito_user_pool_domain.cognito-domain.domain
  description = "Domain of the Cognito User Pool"
}

# output sample
# [
#   "123greed",
#   "456greed",
#   ...
# ]
output "app_client_ids" {
  value       = [for client in aws_cognito_user_pool_client.clients : client.id]
  description = "IDs of the Cognito User Pool Clients"
}


# output sample
# {
#   "tenant1"     = "123greed",
#   "tenant2"   = "456greed",
#   ...
# }
output "app_client_ids_map" {
  value = { for client in aws_cognito_user_pool_client.clients : client.name => client.id }
  description = "Map from client name to Cognito App Client ID"
}
