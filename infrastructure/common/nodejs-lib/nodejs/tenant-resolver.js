const TENANT_DOMAIN_MAP = {
  // CloudFront (admin-side) domain
  // TODO
  'https://do268epc5wmsb.cloudfront.net': 1,
  'https://d18bnb2abbw8ps.cloudfront.net': 1,

  // CloudFront (auction-side) domain
  'https://d13mmha1gm19pv.cloudfront.net': 1,
  'https://d1oyugk53exolc.cloudfront.net': 1,

  'http://localhost:3000': 1, // for local development(admin-side)
  'http://localhost:5173': 1, // for local development(auction-side)
  'http://localhost:5174': 1, // for local development(auction-side)
  'http://localhost:5175': 1, // for local development(auction-side)

  // Tenant 1 domains
  'https://saas-1-admin.gmo.maphin.jp': 1,
  'https://saas-1-auction.gmo.maphin.jp': 1,

  // Tenant 2 domains
  'https://saas-2-admin.gmo.maphin.jp': 2,
  'https://saas-2-auction.gmo.maphin.jp': 2,

  // Tenant 3 domains
  'https://saas-3-admin.gmo.oec-tokyo.com': 3,
  'https://saas-3-auction.gmo.oec-tokyo.com': 3,
}

/**
 * Tenant resolver utility
 * Identifies tenant based on origin
 */
function resolveTenant(event) {
  console.log('find tenant ID from origin path!')
  const origin = event.headers?.origin
  if (origin && TENANT_DOMAIN_MAP[origin]) {
    console.log(`Tenant resolved from hostname: ${origin}`)
    return {
      error: false,
      data: TENANT_DOMAIN_MAP[origin],
    }
  }

  const errorResponse = {
    error: true,
    message: 'Unable to resolve domain',
  }
  console.error(
    'Unable to resolve tenant from domain: ' + (origin || 'unknown')
  )
  return errorResponse
}

module.exports = {
  resolveTenant,
}
