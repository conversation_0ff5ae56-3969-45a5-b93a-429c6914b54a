resource "aws_cognito_user_pool" "auction_user_pool" {
  name = "${var.project_name}-${var.environment}-auction-side"

  # Allow users to sign in with email instead of username
  username_attributes = ["email"]
  auto_verified_attributes = ["email"]

  # Password policy
  password_policy {
    minimum_length                    = var.password_policy.minimum_length
    require_lowercase                 = var.password_policy.require_lowercase
    require_numbers                   = var.password_policy.require_numbers
    require_symbols                   = var.password_policy.require_symbols
    require_uppercase                 = var.password_policy.require_uppercase
    temporary_password_validity_days  = var.password_policy.temporary_password_validity_days
  }

  # Auction-specific custom attributes
  schema {
    name                     = "member_no"
    attribute_data_type      = "Number"
    mutable                  = true
    required                 = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                     = "user_no"
    attribute_data_type      = "Number"
    mutable                  = true
    required                 = false
    number_attribute_constraints {
      min_value = "0"
    }
  }
  schema {
    name                     = "member_name"
    attribute_data_type      = "String"
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      max_length = "100"
    }
  }
  schema {
    name                     = "language_code"
    attribute_data_type      = "String"
    mutable                  = true
    required                 = false
    string_attribute_constraints {
      max_length = "2"
    }
  }

  # Email verification configuration
  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
    email_subject = "[${var.project_name}] 検証コード"
    email_message = "検証コードは「{####}」です。"
  }

  # MFA is set to OFF - no email verification on login
  mfa_configuration = "OFF"

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }
}

# Create tenant groups for auction users
resource "aws_cognito_user_group" "auction_tenant_groups" {
  for_each = toset(var.tenant_ids)
  name        = "tenant-id:${each.value}"
  user_pool_id = aws_cognito_user_pool.auction_user_pool.id
  description = each.value == "0" || each.value == 0 ? "システム管理者用グループ" : "テナント${each.value}用グループ"
}

# Create app client for auction site
resource "aws_cognito_user_pool_client" "auction_client" {
  name                   = "auction-client"
  user_pool_id           = aws_cognito_user_pool.auction_user_pool.id
  generate_secret        = false
  refresh_token_validity = 30
  access_token_validity  = 1
  id_token_validity      = 1

  callback_urls          = var.callback_urls
  logout_urls            = var.logout_urls

  allowed_oauth_flows    = ["code", "implicit"]
  allowed_oauth_scopes   = ["email", "openid", "profile"]

  # Security setting to prevent user enumeration attacks
  prevent_user_existence_errors = "ENABLED"

  # Authentication flows allowed for this client application
  explicit_auth_flows = [
    "ALLOW_REFRESH_TOKEN_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_ADMIN_USER_PASSWORD_AUTH"
  ]
}

# Create domain for auction user pool
resource "aws_cognito_user_pool_domain" "auction_cognito_domain" {
  domain       = "${var.domain_prefix}-auction"
  user_pool_id = aws_cognito_user_pool.auction_user_pool.id
}
