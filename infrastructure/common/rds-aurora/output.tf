output "database-cluster-identifier" {
  description = "database-cluster-identifie"
  value       = "${var.environment}-${var.project_name}-database-cluster"
}

output "database-cluster-arn" {
  description = "database-cluster-arn"
  value       = aws_rds_cluster.database_cluster.arn
}

output "master_username" {
  description = "master_username"
  value       = aws_rds_cluster.database_cluster.master_username
}

output "master_password" {
  description = "master_password"
  value       = aws_rds_cluster.database_cluster.master_password
}

output "database_name" {
  description = "database_name"
  value       = aws_rds_cluster.database_cluster.database_name
}

output "port" {
  description = "port"
  value       = aws_rds_cluster.database_cluster.port
}


# Main Proxy (READ/WRITE)
output "proxy_endpoint" {
  description = "proxy_endpoint"
  value       = aws_db_proxy.database_proxy.endpoint
}

# Read-Only Proxy (READ-ONLY)
output "proxy_read_only_endpoint" {
  description = "proxy_read_only_endpoint"
  value       = aws_db_proxy_endpoint.read_only.endpoint
}
