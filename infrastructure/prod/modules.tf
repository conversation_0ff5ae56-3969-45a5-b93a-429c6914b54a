module "common" {
  source                                      = "../common"
  project_name                                = local.project_name
  environment                                 = local.environment
  basic_auth_enable                           = local.basic_auth_enable
  admin_basic_auth_enable                     = local.admin_basic_auth_enable
  profile_name                                = local.profile_name
  external_domain_name                        = local.external_domain_name
  auction_domain_name                         = local.auction_domain_name
  admin_domain_name                           = local.admin_domain_name
  web_socket_domain                           = local.web_socket_domain
  mail_from_domain                            = local.mail_from_domain
  record_name                                 = local.record_name
  s3-bucket-arn                               = aws_s3_bucket.bucket.arn
  s3-bucket-id                                = aws_s3_bucket.bucket.id
  s3-bucket-bucket                            = aws_s3_bucket.bucket.bucket
  bucket_regional_domain_name                 = aws_s3_bucket.bucket.bucket_regional_domain_name
  api_gateway_5xx_alarm_slack_hook_url        = local.api_gateway_5xx_alarm_slack_hook_url
  nat_gateway_amount                          = local.nat_gateway_amount
  admin_lambda_global_environment_variables   = local.admin_lambda_global_environment_variables
  auction_lambda_global_environment_variables = local.auction_lambda_global_environment_variables
  serverless_min_capacity                     = local.serverless_min_capacity
  serverless_max_capacity                     = local.serverless_max_capacity
  batch-cron-rule                             = "rate(1 minute)"
  admin_tenants                               = local.admin_tenants
  admin_custom_domains                        = local.admin_custom_domains
  admin_acm_domain_name                       = local.admin_acm_domain_name
  auction_tenants                             = local.auction_tenants
  auction_custom_domains                      = local.auction_custom_domains
  auction_acm_domain_name                     = local.auction_acm_domain_name
}

# module "ec2-rds-cron-job" {
#   source           = "../modules/ec2-rds-cron-job"
#   project_name     = local.project_name
#   environment      = local.environment
#   rds-instance     = module.common.database-cluster-identifier
#   rds-instance-arn = module.common.database-cluster-arn
#   ec2-instance     = module.common.ec2-instance
#   ec2-instance-arn = module.common.ec2-instance-arn
#   start-cron-rule  = "30 23 ? * SUN-THU *" // 開始 at 8:30 時 UTC+9 (23:30 UTC) on 月〜金
#   stop-cron-rule   = "59 14 ? * MON-FRI *" // 停止 at 11:59 時 UTC+9 (14:59 UTC) on 月〜金
# }
