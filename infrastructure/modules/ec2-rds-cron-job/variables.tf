variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "rds-instance" {
  description = "rds-instance"
}

variable "rds-instance-arn" {
  description = "rds-instance-arn"
}

variable "ec2-instance" {
  description = "ec2-instance"
}

variable "ec2-instance-arn" {
  description = "ec2-instance-arn"
}

variable "start-cron-rule" {
  default = ""
}

variable "stop-cron-rule" {
  default = ""
}

variable "enable_rds_scheduling" {
  description = "Whether to include RDS instances in the automated start/stop cron job scheduling"
  type        = bool
  default     = true
}
