-- SEQUENCE: public.m_field_mapping_no_seq

-- DROP SEQUENCE public.m_field_mapping_no_seq;

CREATE SEQUENCE public.m_field_mapping_no_seq
INCREMENT 1
START 1
MINVALUE 1
MAXVALUE 9223372036854775807
CACHE 1;

ALTER SEQUENCE public.m_field_mapping_no_seq OWNER TO postgres;

CREATE TABLE IF NOT EXISTS public.m_field_mapping (
    field_mapping_no bigint NOT NULL,
    tenant_no bigint NOT NULL,
    window_id character varying NOT NULL,
    window_field_id character varying,
    language_code character varying NOT NULL,
    field_no jsonb NOT NULL,
    create_datetime timestamp with time zone DEFAULT now(),
    update_datetime timestamp with time zone DEFAULT now(),
    delete_flag integer,
    CONSTRAINT m_field_mapping_pkey PRIMARY KEY (field_mapping_no),
    CONSTRAINT m_field_mapping_tenant_no_fkey FOREIGN KEY (tenant_no)
    REFERENCES public.m_tenant (tenant_no) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE RESTRICT
)
WITH (
    OIDS = FALSE
)
TABLESPACE pg_default;

ALTER TABLE public.m_field_mapping
OWNER TO postgres;

ALTER TABLE ONLY public.m_field_mapping
ALTER COLUMN field_mapping_no SET DEFAULT nextval('m_field_mapping_no_seq'::regclass);

COMMENT ON TABLE public.m_field_mapping IS '項目マッピングマスタ';
COMMENT ON COLUMN public.m_field_mapping.field_mapping_no IS '項目番号';
COMMENT ON COLUMN public.m_field_mapping.tenant_no IS 'テナント番号';
COMMENT ON COLUMN public.m_field_mapping.window_id IS '画面ID';
COMMENT ON COLUMN public.m_field_mapping.window_field_id IS '画面の項目ID';
COMMENT ON COLUMN public.m_field_mapping.language_code IS '言語区分';
COMMENT ON COLUMN public.m_field_mapping.field_no IS '項目情報配列（JSONB形式）';
COMMENT ON COLUMN public.m_field_mapping.create_datetime IS '作成日時';
COMMENT ON COLUMN public.m_field_mapping.update_datetime IS '更新日時';
COMMENT ON COLUMN public.m_field_mapping.delete_flag IS '削除フラグ';
