-- SEQUENCE: public.m_external_field_mapping_no_seq

-- DROP SEQUENCE public.m_external_field_mapping_no_seq;

CREATE SEQUENCE public.m_external_field_mapping_no_seq
INCREMENT 1
START 1
MINVALUE 1
MAXVALUE 9223372036854775807
CACHE 1;

ALTER SEQUENCE public.m_external_field_mapping_no_seq OWNER TO postgres;

CREATE TABLE IF NOT EXISTS public.m_external_field_mapping (
    external_field_mapping_no bigint NOT NULL,
    tenant_no bigint NOT NULL,
    target_object character varying NOT NULL,
    external_system bigint NOT NULL,
    field_localized_no bigint NOT NULL,
    external_system_item_key character varying NOT NULL,
    editable_flag integer NOT NULL DEFAULT 0,
    admin_display_flag integer NOT NULL DEFAULT 0,
    member_display_flag integer NOT NULL DEFAULT 0,
    create_datetime timestamp with time zone DEFAULT now(),
    update_datetime timestamp with time zone DEFAULT now(),
    delete_flag integer DEFAULT 0,
    CONSTRAINT m_external_field_mapping_pkey PRIMARY KEY (external_field_mapping_no),
    CONSTRAINT m_external_field_mapping_tenant_no_fkey FOREIGN KEY (tenant_no)
    REFERENCES public.m_tenant (tenant_no) MATCH SIMPLE
    ON UPDATE RESTRICT
    ON DELETE RESTRICT
)
WITH (
    OIDS = FALSE
)
TABLESPACE pg_default;

ALTER TABLE public.m_external_field_mapping
OWNER TO postgres;

ALTER TABLE ONLY public.m_external_field_mapping
ALTER COLUMN external_field_mapping_no SET DEFAULT nextval('m_external_field_mapping_no_seq'::regclass);

COMMENT ON TABLE public.m_external_field_mapping
IS '外部項目マッピングマスタ';

COMMENT ON COLUMN public.m_external_field_mapping.external_field_mapping_no
IS '外部項目マッピング番号';

COMMENT ON COLUMN public.m_external_field_mapping.tenant_no
IS 'テナント番号';

COMMENT ON COLUMN public.m_external_field_mapping.target_object
IS '対象';

COMMENT ON COLUMN public.m_external_field_mapping.external_system
IS '外部システム';

COMMENT ON COLUMN public.m_external_field_mapping.field_localized_no
IS '項目（多言語化）番号';

COMMENT ON COLUMN public.m_external_field_mapping.external_system_item_key
IS '外部システム項目名';

COMMENT ON COLUMN public.m_external_field_mapping.editable_flag
IS '編集可能フラグ';

COMMENT ON COLUMN public.m_external_field_mapping.admin_display_flag
IS '管理者表示フラグ';

COMMENT ON COLUMN public.m_external_field_mapping.member_display_flag
IS '会員表示フラグ';

COMMENT ON COLUMN public.m_external_field_mapping.create_datetime
IS '作成日時';

COMMENT ON COLUMN public.m_external_field_mapping.update_datetime
IS '更新日時';

COMMENT ON COLUMN public.m_external_field_mapping.delete_flag
IS '削除フラグ';
