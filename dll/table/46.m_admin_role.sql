-- Table: public.m_admin_role

-- DROP TABLE IF EXISTS public.m_admin_role;

CREATE SEQUENCE m_admin_role_no_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE TABLE IF NOT EXISTS public.m_admin_role
(
    admin_role_no bigint NOT NULL DEFAULT nextval('m_admin_role_no_seq'::regclass),
    tenant_no bigint NOT NULL,
    target_group_id integer NOT NULL,
    function_id bigint NOT NULL,
    function_name character varying COLLATE pg_catalog."default",
    allowed_role_id text[] COLLATE pg_catalog."default" NOT NULL,
    create_admin_no bigint,
    create_datetime timestamp with time zone DEFAULT now(),
    update_admin_no bigint,
    update_datetime timestamp with time zone DEFAULT now(),
    CONSTRAINT m_admin_role_pkey PRIMARY KEY (admin_role_no)
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS public.m_admin_role
    OWNER to postgres;
