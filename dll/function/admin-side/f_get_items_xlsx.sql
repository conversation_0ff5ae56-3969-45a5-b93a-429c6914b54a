CREATE OR REPLACE FUNCTION public.f_get_items_xlsx (
    in_tenant_no bigint,
    in_exhibition_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    exhibition_no bigint,
    items jsonb[],
    constant_key_strings text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- ファイルダウンロード用の出展情報を取得する
--  in_exhibition_no　がnull の場合は、1行の例のitem項目設定を取得する
--  in_exhibition_no　が指定されている場合は、出展会に紐づく出展情報を取得する
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH
  getLanguages AS (
    SELECT
        value1 as language_code,
        value2
      FROM f_get_constants_by_keys (
        ARRAY['LANGUAGE_CODE'],
        in_tenant_no,
        null
      ) c
      JOIN m_tenant mt
        ON mt.tenant_no = in_tenant_no
        AND mt.language_code_list @> ARRAY[c.value1]
      WHERE c.value1 = in_language_code
  ),
  getExhibitionItems AS (
    SELECT TE.tenant_no,
           TE.exhibition_no,
           TEI.lot_no,
           TLD.item_no,
           I.manage_no,
           TEI.quantity,
           TEI.lowest_bid_price,
           TEI.lowest_bid_accept_price,
           TEI.lowest_bid_quantity,
           TEI.lowest_bid_accept_quantity,
           TEI.default_end_datetime,
           TE.preview_start_datetime,
           TE.preview_end_datetime,
           I.price_display_flag,
           TEI.recommend_flag,
           I.area_id,
           I.status,
           TE.currency_id
      FROM t_exhibition TE
      LEFT JOIN t_exhibition_item TEI
        ON TE.exhibition_no = TEI.exhibition_no
      LEFT JOIN t_lot_detail TLD
        ON TEI.lot_no = TLD.lot_no
      LEFT JOIN t_item I
        ON TLD.item_no = I.item_no
       AND COALESCE(I.delete_flag, 0) = 0
      WHERE TE.exhibition_no = in_exhibition_no
        AND TE.tenant_no = in_tenant_no
        AND COALESCE(TE.delete_flag, 0) = 0
        AND COALESCE(TEI.delete_flag, 0) = 0
  ),
  getItemDetail AS (
    SELECT
      tl.item_localized_no,
      t.item_no,
      t.manage_no,
      t.area_id,
      t.status,
      t.recommend_flag,
      f.field_division,
      fl.language_code,
      array_agg(
        jsonb_build_object(
          'field_no', f.field_no,
          'physical_name', f.physical_name,
          'logical_name', fl.logical_name,
          'value', tl.free_field ->> f.physical_name,
          'data_type', f.data_type,
          'input_type', f.input_type,
          'input_data_list', f.input_data_list,
          'required_flag', f.required_flag,
          'order', f.order_no
        )
        ORDER BY f.order_no
      ) AS field_map
    FROM m_field f
    -- Get localized fields for the specified language
    INNER JOIN m_field_localized fl
      ON fl.field_no = f.field_no
      AND fl.tenant_no = f.tenant_no
      AND COALESCE(fl.delete_flag, 0) = 0
    LEFT JOIN getExhibitionItems t
      ON t.tenant_no = f.tenant_no
    LEFT JOIN t_item_localized tl
      ON tl.item_no = t.item_no
      AND tl.tenant_no = t.tenant_no
      AND tl.language_code = fl.language_code
      AND COALESCE(tl.delete_flag, 0) = 0
    LEFT JOIN getLanguages gl
      ON fl.language_code = gl.language_code
    WHERE
      fl.field_no IS NOT NULL
      AND gl.language_code IS NOT NULL
      AND f.tenant_no = in_tenant_no
      AND f.field_division = 'item' -- Division for item fields
      AND COALESCE(f.delete_flag, 0) = 0
    GROUP BY tl.item_localized_no, t.item_no, t.manage_no, t.area_id, t.status, t.recommend_flag, f.field_division, fl.language_code
    ORDER BY tl.item_localized_no
  ),
  -- Get all constant keys that use in field mapping
  getConstantKeys AS (
    SELECT array_agg(DISTINCT key_string) AS key_strings
      FROM (
        SELECT DISTINCT fm.field->'input_data_list'->>'key_string' AS key_string
          FROM getItemDetail GID
          JOIN LATERAL unnest(GID.field_map) AS fm(field)
            ON fm.field->'input_data_list'->>'key_string' IS NOT NULL
          WHERE GID.field_map IS NOT NULL
      )
  ),
  -- Collect localized fields for the item
  localized AS (
    SELECT GID.item_no
         , GID.manage_no
         , GID.area_id
         , GID.status
         , GID.recommend_flag
         , array_agg(
            jsonb_build_object(
              'language_code', GID.language_code,
              'field_map', GID.field_map
            )
          ) localized_json_array
      FROM getItemDetail GID
     GROUP BY GID.item_no, GID.manage_no, GID.area_id, GID.status, GID.recommend_flag
  ),
  -- Collect item details for the exhibition
  itemDetails AS (
    SELECT TE.exhibition_no,
            array_agg(
              jsonb_build_object(
                'item_no', L.item_no,
                'manage_no', L.manage_no,
                'exhibition_no', TE.exhibition_no,
                'area_id', L.area_id,
                'status', L.status,
                'recommend_flag', L.recommend_flag,
                'quantity', TE.quantity,
                'lowest_bid_price', TE.lowest_bid_price,
                'lowest_bid_accept_price', TE.lowest_bid_accept_price,
                'lowest_bid_quantity', TE.lowest_bid_quantity,
                'lowest_bid_accept_quantity', TE.lowest_bid_accept_quantity,
                'default_end_datetime', TE.default_end_datetime,
                'preview_start_datetime', TE.preview_start_datetime,
                'preview_end_datetime', TE.preview_end_datetime,
                'price_display_flag', TE.price_display_flag,
                'localized_json_array', L.localized_json_array,
                'currency_id', TE.currency_id
              )
            ) AS items
    FROM localized L
    LEFT JOIN getExhibitionItems TE
      ON L.item_no = TE.item_no
    GROUP BY TE.exhibition_no
  )

  SELECT I.exhibition_no
        , I.items
        , C.key_strings AS constant_key_strings
    FROM itemDetails I
    LEFT JOIN getConstantKeys C
      ON TRUE
  ;

END;

$BODY$;
