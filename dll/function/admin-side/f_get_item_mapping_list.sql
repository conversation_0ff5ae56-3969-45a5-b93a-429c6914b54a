CREATE OR REPLACE FUNCTION public.f_get_item_mapping_list(
    in_tenant_no bigint,
    in_external_system_no bigint,
    in_target_object character varying
)
RETURNS TABLE(
    external_field_mapping_no bigint,
    field_localized_no bigint,
    auction_field_name text,
    external_system_item_key character varying,
    editable_flag integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 項目マッピング管理のリストを取得する
-- Parameters
-- @param in_tenant_no character varying - テナント番号
-- @param in_external_system_no bigint - 外部システム番号
-- @param in_target_object character varying - 対象オブジェクト
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT efm.external_field_mapping_no AS external_field_mapping_no
        ,efm.field_localized_no AS field_localized_no
        ,COALESCE(fl.logical_name, '') || '(' || COALESCE(f.physical_name, '') || ')' AS auction_field_name
        ,efm.external_system_item_key AS external_system_item_key
        ,efm.editable_flag AS editable_flag
    FROM
      m_external_field_mapping efm
    JOIN
      m_field_localized fl
    ON efm.field_localized_no = fl.field_localized_no
    JOIN
      m_field f
    ON fl.field_no = f.field_no
    WHERE f.tenant_no = in_tenant_no
      AND fl.tenant_no = in_tenant_no
      AND efm.tenant_no = in_tenant_no
      AND efm.external_system = in_external_system_no
      AND efm.target_object = in_target_object
      AND f.delete_flag = 0
      AND fl.delete_flag = 0
      AND efm.delete_flag = 0
    ORDER BY efm.external_field_mapping_no;

END;
$BODY$;
