CREATE OR REPLACE FUNCTION public.f_upsert_exhibitions (
    in_exhibition_no bigint,
    in_tenant_no bigint,
    in_category_id integer,
    in_pitch_option integer,
    in_end_option integer,
    in_exhibition_localized_json jsonb,
    in_preview_start_datetime character varying,
    in_preview_end_datetime character varying,
    in_start_datetime character varying,
    in_end_datetime character varying,
    in_max_extend_datetime character varying,
    in_extend_judge_minutes integer,
    in_extend_minutes integer,
    in_pitch_width numeric,
    in_more_little_judge_pitch integer,
    in_exhibition_classification_info jsonb,
    in_join_object integer,
    in_admin_no bigint
)
RETURNS TABLE(
  ret_status bigint,
  data bigint,
  message character varying,
  localize_error character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

----------------------------------------------------------------------------------------------------
-- 入札会情報を登録・更新する
-- Parameters
-- @param in_exhibition_no 開催回番号
-- @param in_tenant_no テナント番号
-- @param in_category_id カテゴリー
-- @param in_pitch_option 入札単位区分
-- @param in_end_option 終了区分
-- @param in_exhibition_localized_json 開催回多言語対応項目連想配列（KiTは開催回名のみ）
-- @param in_preview_start_datetime 下見開始日時
-- @param in_preview_end_datetime 開催回終了日時
-- @param in_start_datetime 開催回開始日時
-- @param in_end_datetime 開催回終了日時
-- @param in_max_extend_datetime 最大延長時刻
-- @param in_extend_judge_minutes 延長開始判定分数
-- @param in_extend_minutes 延長加算分数
-- @param in_pitch_width ピッチ幅
-- @param in_more_little_judge_pitch あと少し表示ピッチ
-- @param in_exhibition_classification_info 開催回設定情報
-- @param in_admin_no 管理者番号
-- @returns return_exhibition_no 開催回番号
----------------------------------------------------------------------------------------------------

DECLARE
  return_exhibition_no bigint;
  in_language_code text;
  ret_result boolean;
  ret_status bigint;
  ret_data bigint;
  ret_message character varying;
  ret_localize_error character varying;
  v_currency_id character varying;

BEGIN

  ret_result := true;
  ret_status := 200;
  ret_data := null;
  ret_message := '';
  ret_localize_error := null;

  FOR in_language_code IN SELECT jsonb_object_keys(in_exhibition_localized_json)
  LOOP
    IF EXISTS (
      SELECT 1 FROM t_exhibition TE
      INNER JOIN t_exhibition_localized TEL
          ON TE.exhibition_no = TEL.exhibition_no
          AND TEL.language_code = in_language_code
          AND TEL.exhibition_name = in_exhibition_localized_json->in_language_code->>'exhibition_name'
          AND TEL.delete_flag = 0
          AND CASE WHEN in_exhibition_no IS NOT NULL THEN TEL.exhibition_no <> in_exhibition_no ELSE true END
      WHERE TE.tenant_no = in_tenant_no
--       AND TE.category_id = in_category_id　入札会カテゴリが違う場合でもエラーとする
    ) THEN
      ret_result := false;
      ret_status := 400;
      ret_data := null;
      ret_message := 'E000232';
      ret_localize_error := in_exhibition_localized_json->in_language_code->>'language_label';
    END IF;
  END LOOP;

  SELECT
    currency_code
  INTO
    v_currency_id
  FROM
    m_tenant
  WHERE
    tenant_no = in_tenant_no
  ;

  IF ret_result = true THEN

    IF in_exhibition_no IS NULL THEN

      INSERT INTO t_exhibition (
          tenant_no
        , category_id
        , pitch_option
        , end_option
        , preview_start_datetime
        , preview_end_datetime
        , start_datetime
        , end_datetime
        , max_extend_datetime
        , extend_judge_minutes
        , extend_minutes
        , currency_id
        , pitch_width
        , more_little_judge_pitch
        , exhibition_classification_info
        , join_object
        , create_admin_no
        , update_admin_no
      ) VALUES (
          in_tenant_no
        , in_category_id
        , in_pitch_option
        , in_end_option
        , in_preview_start_datetime::timestamp with time zone
        , in_preview_end_datetime::timestamp with time zone
        , in_start_datetime::timestamp with time zone
        , in_end_datetime::timestamp with time zone
        , in_max_extend_datetime::timestamp with time zone
        , in_extend_judge_minutes
        , in_extend_minutes
        , v_currency_id
        , in_pitch_width
        , in_more_little_judge_pitch
        , in_exhibition_classification_info
        , in_join_object
        , in_admin_no
        , in_admin_no
      )
      RETURNING exhibition_no INTO return_exhibition_no;

      FOR in_language_code IN SELECT jsonb_object_keys(in_exhibition_localized_json)
      LOOP
        INSERT INTO t_exhibition_localized (
            tenant_no
          , exhibition_no
          , language_code
          , exhibition_name
          , create_admin_no
          , update_admin_no
        ) VALUES (
            in_tenant_no
          , return_exhibition_no
          , in_language_code
          , in_exhibition_localized_json->in_language_code->>'exhibition_name'
          , in_admin_no
          , in_admin_no
        );
      END LOOP;

      INSERT INTO t_exhibition_summary (
          tenant_no
        , exhibition_no
        , create_admin_no
        , update_admin_no
      ) VALUES (
          in_tenant_no
        , return_exhibition_no
        , in_admin_no
        , in_admin_no
      );

    ELSE

      UPDATE t_exhibition
         SET preview_start_datetime = in_preview_start_datetime::timestamp with time zone
           , preview_end_datetime = in_preview_end_datetime::timestamp with time zone
           , start_datetime = in_start_datetime::timestamp with time zone
           , end_datetime = in_end_datetime::timestamp with time zone
           , max_extend_datetime = in_max_extend_datetime::timestamp with time zone
           , extend_judge_minutes = in_extend_judge_minutes
           , extend_minutes = in_extend_minutes
           , pitch_width = in_pitch_width
           , category_id = in_category_id
           , pitch_option = in_pitch_option
           , end_option = in_end_option
           , more_little_judge_pitch = in_more_little_judge_pitch
           , exhibition_classification_info = in_exhibition_classification_info
           , update_admin_no = in_admin_no
           , join_object = in_join_object
           , update_datetime = now()
       WHERE exhibition_no = in_exhibition_no
      RETURNING exhibition_no INTO return_exhibition_no;

      FOR in_language_code IN SELECT jsonb_object_keys(in_exhibition_localized_json)
      LOOP
        -- 既存レコードが存在するかチェック
        IF EXISTS (
          SELECT 1 FROM t_exhibition_localized
          WHERE exhibition_no = return_exhibition_no
            AND language_code = in_language_code
        ) THEN
          -- 既存レコードを更新
          UPDATE t_exhibition_localized
             SET exhibition_name = in_exhibition_localized_json->in_language_code->>'exhibition_name'
               , update_admin_no = in_admin_no
               , update_datetime = now()
           WHERE exhibition_no = return_exhibition_no
             AND language_code = in_language_code;
        ELSE
          -- 新しい言語のレコードを挿入
          INSERT INTO t_exhibition_localized (
              tenant_no
            , exhibition_no
            , language_code
            , exhibition_name
            , create_admin_no
            , update_admin_no
          ) VALUES (
              in_tenant_no
            , return_exhibition_no
            , in_language_code
            , in_exhibition_localized_json->in_language_code->>'exhibition_name'
            , in_admin_no
            , in_admin_no
          );
        END IF;
      END LOOP;

    END IF;

    ret_data := return_exhibition_no;

  END IF;

RETURN QUERY
SELECT ret_status,
      ret_data,
      ret_message,
      ret_localize_error
;

END;

$BODY$;
