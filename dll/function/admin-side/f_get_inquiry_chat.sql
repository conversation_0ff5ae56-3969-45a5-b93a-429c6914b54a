CREATE OR REPLACE FUNCTION public.f_get_inquiry_chat (
  in_tenant_no bigint,
  in_exhibition_item_no bigint
)
RETURNS TABLE(
  exhibition_message_no bigint,
  update_category_id character varying,
  answer_exhibition_message_no bigint,
  message character varying,
  member_no bigint,
  member_name text,
  checked_admin_no bigint,
  checked_admin_name character varying,
  create_admin_no bigint,
  create_admin_name character varying,
  create_datetime character varying,
  answer_flag integer,
  delete_flag integer,
  no_answer_flag integer,
  hidden_flag integer
)
LANGUAGE 'plpgsql'

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
--  問い合わせチャット情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TEM.exhibition_message_no,
         TEM.update_category_id,
         TEM.answer_exhibition_message_no,
         TEM.message,
         TEM.member_no,
         MM.free_field->>'memberName' member_name,
         TEM.checked_admin_no,
         MA.admin_name as checked_admin_name,
         TEM.create_admin_no,
         MA2.admin_name as create_admin_name,
         to_char(TEM.create_datetime, 'YYYY/MM/DD HH24:MI') :: character varying create_datetime,
         CASE WHEN TEM.update_category_id = '2' AND TEM.checked_admin_no IS NOT NULL AND TEM.delete_flag = 0 AND TEM.no_answer_flag = 0 THEN 1
              ELSE 0
              END as answer_flag,
         TEM.delete_flag,
         TEM.no_answer_flag,
         TEM.hidden_flag
    FROM t_exhibition_message TEM
    LEFT OUTER JOIN m_member MM
	    ON MM.member_no = TEM.member_no
     AND MM.tenant_no = in_tenant_no
    LEFT OUTER JOIN m_admin MA
	    ON MA.admin_no = TEM.checked_admin_no
     AND MA.tenant_no = in_tenant_no
    LEFT OUTER JOIN m_admin MA2
	    ON MA2.admin_no = TEM.create_admin_no
     AND MA2.tenant_no = in_tenant_no
   WHERE TEM.tenant_no = in_tenant_no
     AND TEM.exhibition_item_no = in_exhibition_item_no
ORDER BY COALESCE(TEM.answer_exhibition_message_no, TEM.exhibition_message_no), TEM.create_datetime;

END;

$BODY$;
