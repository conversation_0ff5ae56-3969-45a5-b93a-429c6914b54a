CREATE OR REPLACE FUNCTION public.f_get_tenant_setting (
    IN in_tenant_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    tenant_name character varying,
    company_name character varying,
    contact_email character varying,
    two_factor_auth_required integer,
    currency_code character varying,
    language_code_list character varying[],
    search_result_view_mode character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- テナント情報を取得する
-- Parameters
-- @param
--   in_tenant_no bigint
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  SELECT
    T.tenant_no,
    T.tenant_name,
    T.company_name,
    T.contact_email,
    T.two_factor_auth_required,
    T.currency_code,
    T.language_code_list,
    T.search_result_view_mode
  FROM
    m_tenant T
  WHERE
    T.tenant_no = in_tenant_no
  ;
END;

$BODY$;
