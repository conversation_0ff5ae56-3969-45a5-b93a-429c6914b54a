CREATE OR REPLACE FUNCTION public.f_get_item_detail (
    in_item_no bigint,
    in_tenant_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    lot_no bigint,
    item_no bigint,
    manage_no character varying,
    exhibition_no bigint,
    area_id character varying,
    status integer,
    quantity numeric,
    lowest_bid_price numeric,
    lowest_bid_accept_price numeric,
    lowest_bid_quantity numeric,
    lowest_bid_accept_quantity numeric,
    default_end_datetime timestamp with time zone,
    recommend_flag integer,
    preview_start_datetime timestamp with time zone,
    preview_end_datetime timestamp with time zone,
    price_display_flag integer,
    localized_json_array jsonb[],
    ancillary_json_array jsonb[],
    currency_id character varying,
    constant_key_strings text[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 商品詳細情報を取得する
-- Return item details for each language-code (localized fields and ancillary files)
-- Item fields are mapped to the specified language code. And defined in m_field_mapping.
----------------------------------------------------------------------------------------------------
sold_out_display_date character varying;

BEGIN

  RETURN QUERY
  WITH
  getLanguages AS (
    SELECT
        value1 as language_code,
        value2
      FROM f_get_constants_by_keys (
        ARRAY['LANGUAGE_CODE'],
        in_tenant_no,
        null
      ) c
      JOIN m_tenant mt
        ON mt.tenant_no = in_tenant_no
        AND mt.language_code_list @> ARRAY[c.value1]
  ),
  getItemDetail AS (
    SELECT
      tl.item_localized_no,
      t.item_no,
      t.manage_no,
      f.field_division,
      fl.language_code,
      array_agg(
        jsonb_build_object(
          'field_no', f.field_no,
          'physical_name', f.physical_name,
          'logical_name', fl.logical_name,
          'value', tl.free_field ->> f.physical_name,
          'data_type', f.data_type,
          'input_type', f.input_type,
          'input_data_list', f.input_data_list,
          'required_flag', f.required_flag,
          'order', f.order_no
        )
        ORDER BY f.order_no
      ) AS field_map
    FROM m_field f
    INNER JOIN m_field_localized fl
      ON fl.field_no = f.field_no
      AND fl.tenant_no = f.tenant_no
      AND COALESCE(fl.delete_flag, 0) = 0
    INNER JOIN t_item t
      ON t.tenant_no = f.tenant_no
      AND t.item_no = in_item_no
      AND COALESCE(t.delete_flag, 0) = 0
    LEFT JOIN t_item_localized tl
      ON tl.item_no = t.item_no
      AND tl.tenant_no = t.tenant_no
      AND tl.language_code = fl.language_code
      AND COALESCE(tl.delete_flag, 0) = 0
    LEFT JOIN getLanguages gl
      ON (in_language_code IS NULL OR gl.language_code = in_language_code)
      AND fl.language_code = gl.language_code
    WHERE
      t.item_no IS NOT NULL
      AND fl.field_no IS NOT NULL
      AND gl.language_code IS NOT NULL
      AND f.tenant_no = in_tenant_no
      AND f.field_division = 'item' -- Division for item fields
      AND COALESCE(f.delete_flag, 0) = 0
    GROUP BY tl.item_localized_no, t.item_no, t.manage_no, f.field_division, fl.language_code
    ORDER BY tl.item_localized_no
  ),
  -- Get all constant keys that use in field mapping
  getConstantKeys AS (
    SELECT array_agg(DISTINCT key_string) AS key_strings
      FROM (
        SELECT DISTINCT fm.field->'input_data_list'->>'key_string' AS key_string
          FROM getItemDetail GID
          JOIN LATERAL unnest(GID.field_map) AS fm(field)
            ON fm.field->'input_data_list'->>'key_string' IS NOT NULL
          WHERE GID.field_map IS NOT NULL
      )
  ),
  -- Collect localized fields for the item
  localized AS (
    SELECT GID.item_no
         , GID.manage_no
         , array_agg(
            jsonb_build_object(
              'language_code', GID.language_code,
              'field_map', GID.field_map
            )
          ) localized_json_array
      FROM getItemDetail GID
     GROUP BY GID.item_no, GID.manage_no
  ),
  ancillary AS (
    SELECT L.item_no
         , L.manage_no
         , array_agg(
            jsonb_build_object(
              'language_code', AF.language_code,
              'division', AF.division,
              'serial_number', AF.serial_number,
              'file_path', AF.file_path,
              'postar_file_path', AF.postar_file_path
            )
          ) ancillary_json_array
      FROM localized L
      LEFT JOIN t_item_ancillary_file AF
        ON L.item_no = AF.item_no
       AND COALESCE(AF.delete_flag, 0) = 0
     GROUP BY L.item_no, L.manage_no
  )

  SELECT TLD.lot_no
       , I.item_no
       , I.manage_no
       , TE.exhibition_no
       , I.area_id
       , I.status
       , TEI.quantity
       , TEI.lowest_bid_price
       , TEI.lowest_bid_accept_price
       , TEI.lowest_bid_quantity
       , TEI.lowest_bid_accept_quantity
       , TEI.default_end_datetime
       , TEI.recommend_flag
       , TE.preview_start_datetime
       , TE.preview_end_datetime
       , I.price_display_flag
       , L.localized_json_array
       , A.ancillary_json_array
       , TE.currency_id
       , C.key_strings AS constant_key_strings
    FROM t_item I
    LEFT JOIN localized L
      ON L.item_no = I.item_no
    LEFT JOIN ancillary A
      ON A.item_no = I.item_no
    LEFT JOIN getConstantKeys C
      ON TRUE
    LEFT JOIN t_lot_detail TLD
      ON TLD.item_no = I.item_no
    LEFT JOIN t_exhibition_item TEI
      ON TEI.lot_no = TLD.lot_no
    LEFT JOIN t_exhibition TE
      ON TEI.exhibition_no = TE.exhibition_no
   WHERE I.item_no = in_item_no
    --  AND TLD.lot_no IS NOT NULL
    --  AND (
    --        I.status IN (0,1,2)
    --        OR (I.status = 3 AND linked_flag = 1 AND current_timestamp < (linked_datetime + CAST(sold_out_display_date || ' days' AS interval)))
    --      )
     AND I.tenant_no = in_tenant_no
     AND I.delete_flag = 0;

END;

$BODY$;
