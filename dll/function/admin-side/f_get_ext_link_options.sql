CREATE OR REPLACE FUNCTION public.f_get_ext_link_options (
    IN in_tenant_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    ext_link_no bigint,
    ext_link_options jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

----------------------------------------------------------------------------------------------------
-- 外部連携設定を取得します
-- Parameters
-- @param in_tenant_no テナント番号
----------------------------------------------------------------------------------------------------
BEGIN
  RETURN QUERY
  SELECT
    EL.tenant_no,
    EL.ext_link_no,
    EL.ext_link_options
  FROM
    m_ext_link EL
  WHERE
    EL.tenant_no = in_tenant_no
  ;
END;

$BODY$;
