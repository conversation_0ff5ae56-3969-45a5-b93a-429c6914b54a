CREATE OR REPLACE FUNCTION public.f_search_member_by_name(
  in_tenant_no bigint,
  in_name_search_key character varying
)
RETURNS TABLE(
    member_no bigint,
    member_id character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 会員検索
-- Parameters
-- @param tenant_no character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT m.member_no
        ,m.member_id
    FROM m_member m
  WHERE (in_name_search_key is null OR m.member_id LIKE CONCAT('%', in_name_search_key, '%'))
    AND m.tenant_no = in_tenant_no
    AND m.delete_flag = 0
  ;

END;
$BODY$;
