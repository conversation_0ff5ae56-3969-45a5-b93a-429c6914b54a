CREATE OR REPLACE FUNCTION public.f_regist_update_external_field_mapping_list(
    in_tenant_no bigint,
    in_external_system_no bigint,
    in_target_object character varying,
    in_external_field_mappings jsonb,
    in_delete_external_field_mappings jsonb,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- 項目マッピング管理一覧を登録・更新する
-- Parameters
-- @param in_tenant_no bigint
-- @param in_external_system_no bigint
-- @param in_target_object character varying
-- @param in_external_field_mappings jsonb 外部システム項目マッピング
-- @param in_delete_external_field_mappings jsonb 削除対象外部システム項目マッピング
----------------------------------------------------------------------------------------------------

BEGIN

    WITH
    new_external_field_mapping AS (
        INSERT INTO m_external_field_mapping (
            tenant_no,
            target_object,
            external_system,
            field_localized_no,
            external_system_item_key,
            editable_flag,
            admin_display_flag,
            member_display_flag,
            create_datetime,
            update_datetime,
            delete_flag
        )
        (
        SELECT
            in_tenant_no,
            in_target_object,
            in_external_system_no,
            (data->>'field_localized_no')::bigint,
            (data->>'external_system_item_key')::character varying,
            (data->>'editable_flag')::integer,
            0,
            0,
            now(),
            now(),
            0
            FROM jsonb_array_elements(in_external_field_mappings) AS data
            WHERE (data->>'external_field_mapping_no') IS NULL
        )
        RETURNING
            m_external_field_mapping.external_field_mapping_no
    ),

    exit_external_field_mapping AS (
        UPDATE m_external_field_mapping
        SET
            field_localized_no = (data->>'field_localized_no')::bigint,
            external_system_item_key = (data->>'external_system_item_key')::character varying,
            update_datetime = now()
            FROM jsonb_array_elements(in_external_field_mappings) AS data
        WHERE m_external_field_mapping.external_field_mapping_no = (data->>'external_field_mapping_no')::bigint
            AND m_external_field_mapping.tenant_no = in_tenant_no
            AND m_external_field_mapping.editable_flag = 1
            AND m_external_field_mapping.delete_flag = 0
        RETURNING
            m_external_field_mapping.external_field_mapping_no
    ),

    delete_external_field_mapping AS (
        UPDATE m_external_field_mapping
        SET
            delete_flag = 1,
            update_datetime = now()
            FROM jsonb_array_elements(in_delete_external_field_mappings) AS data
        WHERE m_external_field_mapping.external_field_mapping_no = (data->>'external_field_mapping_no')::bigint
            AND m_external_field_mapping.tenant_no = in_tenant_no
            AND m_external_field_mapping.editable_flag = 1
            AND m_external_field_mapping.delete_flag = 0
        RETURNING
            m_external_field_mapping.external_field_mapping_no
    )

    SELECT 200 INTO status FROM
    new_external_field_mapping, exit_external_field_mapping, delete_external_field_mapping;
    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
