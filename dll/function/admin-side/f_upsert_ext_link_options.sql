CREATE OR REPLACE FUNCTION public.f_upsert_ext_link_options (
    in_tenant_no bigint,
    in_ext_link_no bigint,
    in_ext_link_options jsonb,
    in_admin_no bigint
)
RETURNS TABLE (
  upsert_count integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  v_upsert_count integer := 0;
----------------------------------------------------------------------------------------------------
-- 外部連携設定の更新
-- Parameters
-- @param in_tenant_no        テナント番号
-- @param in_ext_link_no      外部連携番号
-- @param in_ext_link_options 外部連携設定
-- @param in_admin_no         管理者番号
----------------------------------------------------------------------------------------------------

BEGIN

  -- m_ext_linkにUPSERT
  INSERT INTO m_ext_link (
    tenant_no,
    ext_link_no,
    ext_link_options,
    create_admin_no,
    create_datetime,
    update_admin_no,
    update_datetime
  ) VALUES (
    in_tenant_no,
    in_ext_link_no,
    in_ext_link_options,
    in_admin_no,
    now(),
    in_admin_no,
    now()
  )
  ON CONFLICT (tenant_no, ext_link_no)
  DO UPDATE SET
    ext_link_options = EXCLUDED.ext_link_options,
    update_admin_no = in_admin_no,
    update_datetime = now()
  ;

  GET DIAGNOSTICS v_upsert_count = ROW_COUNT;

  RETURN QUERY SELECT v_upsert_count;

END;
$BODY$;
