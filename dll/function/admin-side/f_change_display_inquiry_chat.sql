CREATE OR REPLACE FUNCTION public.f_change_display_inquiry_chat (
    in_tenant_no bigint,
    in_exhibition_message_no bigint,
    in_hidden_flag bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- 問合せチャットのメッッセージの表示・非表示を変更
-- Parameters
-- @param in_tenant_no
-- @param in_exhibition_message_no
-- @param in_hidden_flag
----------------------------------------------------------------------------------------------------

BEGIN

  UPDATE t_exhibition_message
     SET hidden_flag = in_hidden_flag
   WHERE exhibition_message_no = in_exhibition_message_no
     AND tenant_no = in_tenant_no;

  SELECT TRUE, 200, NULL
  INTO result,
         status,
         message;
  RETURN NEXT;

END;
$BODY$;
