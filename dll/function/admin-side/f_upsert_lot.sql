CREATE OR REPLACE FUNCTION public.f_upsert_lot (
    in_exhibition_no bigint,
    in_tenant_no bigint,
    in_item_nos bigint[],
    in_lot_no bigint,
    in_quantity numeric,
    in_lowest_bid_quantity numeric,
    in_lowest_bid_accept_quantity numeric,
    in_lowest_bid_price numeric,
    in_lowest_bid_accept_price numeric,
    in_recommend_flag integer,
    in_default_end_datetime character varying,
    in_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- ロット情報の登録・更新をする
----------------------------------------------------------------------------------------------------
return_lot_no bigint;
old_lot_no bigint;
lot_data RECORD;
exhibition_data RECORD;
is_order_no integer;
in_item_no bigint;

BEGIN

  SELECT INTO lot_data l.*
    FROM t_lot l
    LEFT JOIN t_exhibition_item ei ON ei.lot_no = l.lot_no
   WHERE ei.exhibition_no=in_exhibition_no AND l.lot_no=in_lot_no AND l.tenant_no=in_tenant_no AND l.delete_flag = 0;

  -- 新規登録
  IF lot_data.lot_no IS NULL THEN

    -- ロット登録
    WITH get_lot_no AS (
      SELECT nextval('t_lot_no_seq'::regclass) as lot_no
    )
    INSERT INTO t_lot (
      tenant_no,
      lot_no,
      lot_id,
      lowest_bid_price,
      lowest_bid_accept_price,
      create_admin_no,
      update_admin_no
    ) VALUES (
      in_tenant_no,
      (SELECT lot_no FROM get_lot_no),
      (SELECT lot_no FROM get_lot_no),
      in_lowest_bid_price,
      in_lowest_bid_accept_price,
      in_admin_no,
      in_admin_no
    )
    RETURNING lot_no INTO return_lot_no;

    -- 出品数カウントアップ
    UPDATE t_exhibition_summary
       SET exhibition_item_count = (exhibition_item_count + 1)
         , update_admin_no = in_admin_no
         , update_datetime = current_timestamp
     WHERE exhibition_no = in_exhibition_no;

    -- 出品情報登録
    SELECT INTO exhibition_data *
      FROM t_exhibition
     WHERE exhibition_no = in_exhibition_no
       AND tenant_no = in_tenant_no;

    INSERT INTO t_exhibition_item (
      tenant_no,
      exhibition_no,
      lot_no,
      quantity,
      lowest_bid_quantity,
      lowest_bid_accept_quantity,
      lowest_bid_price,
      lowest_bid_accept_price,
      pitch_width,
      start_datetime,
      end_datetime,
      default_end_datetime,
      create_admin_no,
      update_admin_no,
      recommend_flag
    ) VALUES (
      in_tenant_no,
      in_exhibition_no,
      return_lot_no,
      in_quantity,
      in_lowest_bid_quantity,
      in_lowest_bid_accept_quantity,
      in_lowest_bid_price,
      in_lowest_bid_accept_price,
      exhibition_data.pitch_width,
      exhibition_data.start_datetime,
      exhibition_data.end_datetime,
      (CASE WHEN in_default_end_datetime IS NULL
       THEN exhibition_data.end_datetime
       ELSE in_default_end_datetime::timestamp with time zone
      END),
      in_admin_no,
      in_admin_no,
      in_recommend_flag
    );

    is_order_no := 1;
	  FOR idx in 1..array_length(in_item_nos,1)
    LOOP

      old_lot_no := NULL;
      -- 登録されているロット内訳があればそのロット番号を保持しておく
      SELECT INTO old_lot_no l.lot_no
        FROM t_lot l
        LEFT JOIN t_lot_detail ld ON ld.lot_no = l.lot_no
       WHERE ld.item_no=in_item_nos[idx] AND l.lot_no=in_lot_no AND l.tenant_no=in_tenant_no AND l.delete_flag = 0;

      -- ロット内訳を登録する
      INSERT INTO t_lot_detail (
          tenant_no
        , lot_no
        , item_no
        , order_no
        , create_admin_no
        , update_admin_no
      ) VALUES (
          in_tenant_no
        , return_lot_no
        , in_item_nos[idx]
        , is_order_no
        , in_admin_no
        , in_admin_no
      );

      -- 登録先以外のロットにある内訳データを削除する
      DELETE FROM t_lot_detail ld
       USING t_exhibition_item ei
       WHERE ld.item_no = in_item_nos[idx]
         AND ei.lot_no = ld.lot_no
         AND ei.exhibition_no = in_exhibition_no
         AND ei.tenant_no = in_tenant_no
         AND ei.lot_no <> return_lot_no
         AND ei.delete_flag = 0;

      -- 商品情報更新
      UPDATE t_item
         SET status = 2
           , update_admin_no = in_admin_no
           , update_datetime = current_timestamp
      WHERE item_no = in_item_nos[idx] AND tenant_no = in_tenant_no;

      -- 削除したロットが空になった場合はt_lotとt_exhibition_itemを無効化する
      IF old_lot_no IS NOT NULL AND NOT EXISTS (SELECT 1 FROM t_lot_detail WHERE lot_no = old_lot_no AND tenant_no = in_tenant_no) THEN

        UPDATE t_lot l
          SET delete_flag = 1
            , update_admin_no = in_admin_no
            , update_datetime = current_timestamp
        WHERE lot_no = old_lot_no
          AND tenant_no = in_tenant_no
          AND delete_flag = 0;

        UPDATE t_exhibition_item
          SET delete_flag = 1
            , update_admin_no = in_admin_no
            , update_datetime = current_timestamp
        WHERE exhibition_no = in_exhibition_no
          AND lot_no = old_lot_no
          AND tenant_no = in_tenant_no
          AND delete_flag = 0;

        -- 出品数カウントダウン
        UPDATE t_exhibition_summary
           SET exhibition_item_count = (exhibition_item_count - 1)
             , update_admin_no = in_admin_no
             , update_datetime = current_timestamp
         WHERE exhibition_no = in_exhibition_no;

      END IF;

      is_order_no = is_order_no + 1;
    END LOOP;

  -- 既にあるロットデータへの更新
  ELSE

    UPDATE t_lot
       SET lowest_bid_price = in_lowest_bid_price
          , lowest_bid_accept_price = in_lowest_bid_accept_price
          , update_admin_no = in_admin_no
          , update_datetime = current_timestamp
     WHERE lot_no = lot_data.lot_no
       AND tenant_no = in_tenant_no
       AND delete_flag = 0
    RETURNING lot_no INTO return_lot_no;

    UPDATE t_exhibition_item
       SET quantity = in_quantity
          , lowest_bid_quantity = in_lowest_bid_quantity
          , lowest_bid_accept_quantity = in_lowest_bid_accept_quantity
          , lowest_bid_price = in_lowest_bid_price
          , lowest_bid_accept_price = in_lowest_bid_accept_price
          , default_end_datetime = (
              CASE WHEN in_default_end_datetime IS NULL
                  THEN default_end_datetime
                  ELSE in_default_end_datetime::timestamp with time zone
              END)
          , update_admin_no = in_admin_no
          , update_datetime = current_timestamp
          , recommend_flag = in_recommend_flag
     WHERE exhibition_no = in_exhibition_no
       AND lot_no = return_lot_no
       AND tenant_no = in_tenant_no
       AND delete_flag = 0;

    -- ロット内訳に登録されていないを情報を登録する
    is_order_no := 1;
	  FOR idx in 1..array_length(in_item_nos,1)
    LOOP
      IF EXISTS (SELECT 1 FROM t_lot_detail WHERE lot_no = lot_data.lot_no AND item_no = in_item_nos[idx] AND tenant_no = in_tenant_no) THEN
        -- もともと登録されているデータは表示順だけ振り直す
        UPDATE t_lot_detail
           SET order_no = is_order_no
             , update_admin_no = in_admin_no
             , update_datetime = current_timestamp
         WHERE lot_no = lot_data.lot_no
           AND item_no = in_item_nos[idx]
           AND tenant_no = in_tenant_no;
      ELSE

        old_lot_no := NULL;
        -- 別の登録されているロット内訳があればそのロット番号を保持しておく
        SELECT INTO old_lot_no l.lot_no
          FROM t_lot l
          LEFT JOIN t_lot_detail ld ON ld.lot_no = l.lot_no
         WHERE ld.item_no=in_item_nos[idx] AND l.tenant_no=in_tenant_no AND l.delete_flag = 0;

        INSERT INTO t_lot_detail (
            tenant_no
          , lot_no
          , item_no
          , order_no
          , create_admin_no
          , update_admin_no
        ) VALUES (
            in_tenant_no
          , return_lot_no
          , in_item_nos[idx]
          , is_order_no
          , in_admin_no
          , in_admin_no
        );

        -- 登録先以外のロットにある内訳データを削除する
        DELETE FROM t_lot_detail ld
         USING t_exhibition_item ei
         WHERE ld.item_no = in_item_nos[idx]
           AND ei.lot_no = ld.lot_no
           AND ei.exhibition_no = in_exhibition_no
           AND ei.tenant_no = in_tenant_no
           AND ei.lot_no <> return_lot_no
           AND ei.delete_flag = 0;

        -- 削除したロットが空になった場合はt_lotとt_exhibition_itemを無効化する
        IF old_lot_no IS NOT NULL AND NOT EXISTS (SELECT 1 FROM t_lot_detail WHERE lot_no = old_lot_no AND tenant_no = in_tenant_no) THEN

          UPDATE t_lot l
            SET delete_flag = 1
              , update_admin_no = in_admin_no
              , update_datetime = current_timestamp
          WHERE lot_no = old_lot_no
            AND tenant_no = in_tenant_no
            AND delete_flag = 0;

          UPDATE t_exhibition_item
            SET delete_flag = 1
              , update_admin_no = in_admin_no
              , update_datetime = current_timestamp
          WHERE exhibition_no = in_exhibition_no
            AND lot_no = old_lot_no
            AND tenant_no = in_tenant_no
            AND delete_flag = 0;

        END IF;

      END IF;

      -- 商品情報更新
      UPDATE t_item
         SET status = 2
           , update_admin_no = in_admin_no
           , update_datetime = current_timestamp
      WHERE item_no = in_item_nos[idx] AND tenant_no = in_tenant_no;

      is_order_no = is_order_no + 1;
    END LOOP;

  END IF;

  -- 出品数カウントダウン
  UPDATE t_exhibition_summary
     SET exhibition_item_count = (
          SELECT COUNT(*)
            FROM t_exhibition_item EI
           WHERE EI.exhibition_no = in_exhibition_no
               AND EI.delete_flag = 0
               AND EI.cancel_flag = 0
        )
       , update_admin_no = in_admin_no
       , update_datetime = current_timestamp
   WHERE exhibition_no = in_exhibition_no;

  RETURN return_lot_no;

END;

$BODY$;
