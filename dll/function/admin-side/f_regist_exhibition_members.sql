CREATE OR REPLACE FUNCTION public.f_regist_exhibition_members (
    in_tenant_no bigint,
    in_exhibition_no bigint,
    in_member_nos character varying,
    in_admin_no bigint,
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

----------------------------------------------------------------------------------------------------
-- 開催回参加会員追加
----------------------------------------------------------------------------------------------------

BEGIN

  DELETE FROM t_exhibition_member WHERE tenant_no = in_tenant_no AND exhibition_no = in_exhibition_no;
  INSERT INTO t_exhibition_member (
    tenant_no,
    exhibition_no,
    member_no,
    create_admin_no
  )
  (
    SELECT in_tenant_no,
            in_exhibition_no,
            datas,
            in_admin_no
      FROM unnest(in_member_nos::bigint[]) AS datas
  );
  result := true;
  status := 200;
  message := '';

RETURN NEXT;

END;
$BODY$;
