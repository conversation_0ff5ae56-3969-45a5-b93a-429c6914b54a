CREATE OR REPLACE FUNCTION public.f_update_tenant_setting (
    in_tenant_no bigint,
    in_tenant_name character varying,
    in_company_name character varying,
    in_contact_email character varying,
    in_two_factor_auth_required integer,
    in_currency_code character varying,
    in_language_code_list character varying[],
    in_search_result_view_mode character varying
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_tenant_no bigint;

----------------------------------------------------------------------------------------------------
-- テナント情報を更新する
-- Parameters
-- @param
--   in_tenant_no bigint,
--   in_tenant_name character varying,
--   in_company_name character varying,
--   in_contact_email character varying,
--   in_two_factor_auth_required integer,
--   in_currency_code character varying,
--   in_language_code_list character varying[],
--   in_search_result_view_mode character varying
----------------------------------------------------------------------------------------------------

BEGIN
  -- テナント
  WITH update_tenant AS (
    UPDATE
      m_tenant
    SET
      tenant_name = (CASE WHEN in_tenant_name IS NULL THEN tenant_name ELSE in_tenant_name END),
      company_name = (CASE WHEN in_company_name IS NULL THEN company_name ELSE in_company_name END),
      contact_email = (CASE WHEN in_contact_email IS NULL THEN contact_email ELSE in_contact_email END),
      two_factor_auth_required = (CASE WHEN in_two_factor_auth_required IS NULL THEN two_factor_auth_required ELSE in_two_factor_auth_required END),
      currency_code = (CASE WHEN in_currency_code IS NULL THEN currency_code ELSE in_currency_code END),
      language_code_list = (CASE WHEN in_language_code_list IS NULL THEN language_code_list ELSE in_language_code_list END),
      search_result_view_mode = (CASE WHEN in_search_result_view_mode IS NULL THEN search_result_view_mode ELSE in_search_result_view_mode END),
      update_datetime = now()
    WHERE
      tenant_no = in_tenant_no
    RETURNING tenant_no
  )

  SELECT update_tenant.tenant_no FROM update_tenant
  LIMIT 1 INTO return_tenant_no;

  RETURN return_tenant_no;

END;
$BODY$;
