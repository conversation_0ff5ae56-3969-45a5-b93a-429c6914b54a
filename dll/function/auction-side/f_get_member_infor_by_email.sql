CREATE OR REPLACE FUNCTION public.f_get_member_info_by_email (
    in_email character varying,
    in_tenant_no bigint
)
RETURNS TABLE(
    member_no bigint,
    user_no bigint,
    user_id character varying,
    member_id character varying,
    free_field jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員情報取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT MM.member_no,
         MU.user_no,
         MU.user_id,
         MM.member_id,
         MM.free_field
       FROM m_member MM
       JOIN m_user MU ON MU.member_no = MM.member_no
      WHERE MM.tenant_no = in_tenant_no
        AND MM.free_field->>'email' = in_email
      LIMIT 1;
END;

$BODY$;
