CREATE OR REPLACE FUNCTION public.f_cognito_create_member (
    in_tenant_no bigint,
    in_free_field jsonb,
    in_bid_allow_flag integer,
    in_email_delivery_flag integer
)
RETURNS TABLE(
    member_no bigint,
    user_no bigint
)

LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員登録 Cognito経由
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH
  -- 会員申請
  create_member_request AS (
    INSERT INTO t_member_request (
      tenant_no,
      member_request_type,
      free_field,
      delete_flag
    )
    VALUES (
      in_tenant_no,
      1,
      in_free_field,
      1 -- すぐに削除
    )
    RETURNING
      member_request_no,
      email_priority,
      classification,
      free_field
  ),
  -- member_noを取得する
  get_member_no AS (
    SELECT nextval('m_member_no_seq'::regclass) as member_no
  ),
  -- m_memberに情報をコピー
  copy_to_member AS (
    INSERT INTO m_member(
      tenant_no,
      member_no,
      member_id,
      classification,
      currency_id,
      exhibition_allow_flag,
      bid_allow_flag,
      status,
      email_delivery_flag,
      email_priority,
      member_request_no,
      create_datetime,
      update_datetime,
      delete_flag,
      free_field
    )
    VALUES (
      in_tenant_no,
      (SELECT MM.member_no FROM get_member_no MM),
      LPAD((SELECT MM.member_no FROM get_member_no MM)::text,5,'0'),
      (SELECT classification FROM create_member_request),
      (select currency_code from m_tenant where tenant_no = in_tenant_no), -- すべてUSDで固定
      1,
      in_bid_allow_flag,
      1, --管理側で新規登録の場合は承認にする
      in_email_delivery_flag,
      (SELECT email_priority FROM create_member_request),
      (SELECT member_request_no FROM create_member_request),
      now(),
      now(),
      0,
      (SELECT (free_field-'password') FROM create_member_request)
    )
    RETURNING m_member.member_request_no,
              m_member.member_no,
              m_member.member_id,
              m_member.status
  ),
  -- m_userに情報をコピー
  copy_to_user AS (
    INSERT INTO m_user(
      tenant_no,
      member_no,
      user_id,
      password,
      require_password_change,
      require_confirm_token,
      bid_allow_flag,
      free_field,
      create_datetime,
      update_datetime,
      delete_flag
    )
    SELECT in_tenant_no,
            M.member_no,
            CASE WHEN T.login_option = 1 THEN M.member_id
                WHEN T.login_option = 2 THEN (MR.free_field->>'email')::character varying
                ELSE M.member_id
            END,
            '',
            0, --管理側で新規登録の場合はパスワード変更を要求する
            0, --管理側で新規登録の場合は確認トークンを要求する
            in_bid_allow_flag,
            null,
            now(),
            now(),
            0
    FROM copy_to_member M
    JOIN create_member_request MR ON MR.member_request_no = M.member_request_no
    JOIN m_tenant T ON T.tenant_no = in_tenant_no

    RETURNING m_user.user_no
  ),
  -- ステータス履歴に入れる
  insert_history AS (
    INSERT INTO t_member_status_history(
      tenant_no,
      member_request_no,
      user_name,
      before_status,
      after_status,
      create_datetime,
      delete_flag
    ) VALUES (
      in_tenant_no,
      (SELECT MR.member_request_no FROM create_member_request MR),
      'cognito',
      null, -- 管理側で新規登録の場合は変更前ステータスはない
      (SELECT status FROM copy_to_member),
      now(),
      0
    )
    RETURNING member_status_history_no
  )

  SELECT MM.member_no, U.user_no
    FROM copy_to_member MM,copy_to_user U;

END;
$BODY$;
