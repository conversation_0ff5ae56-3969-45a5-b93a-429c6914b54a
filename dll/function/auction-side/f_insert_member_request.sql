CREATE OR REPLACE FUNCTION public.f_insert_member_request (
    in_tenant_no bigint,
    in_member_request_type bigint,
    in_free_field jsonb
)
RETURNS TABLE(
    member_request_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： 会員申請追加
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    INSERT INTO t_member_request (
      tenant_no,
      member_request_type,
      free_field
    )
    VALUES
    (
      in_tenant_no,
      in_member_request_type, -- 「1」固定
      in_free_field
    )
    RETURNING
      t_member_request.member_request_no

END;

$BODY$;
