CREATE OR REPLACE FUNCTION f_get_tenant_settings(
    in_tenant_no bigint
)
RETURNS TABLE(
    tenant_no bigint,
    tenant_id character varying(20),
    tenant_name character varying(100),
    company_name character varying(100),
    contact_email character varying(256),
    domain character varying(100),
    admin_language_code character varying(2),
    language_code_list character varying(512)[],
    function_options jsonb,
    bid_options jsonb,
    search_result_view_mode character varying(10)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT
        mt.tenant_no,
        mt.tenant_id,
        mt.tenant_name,
        mt.company_name,
        mt.contact_email,
        mt.domain,
        mt.admin_language_code,
        mt.language_code_list,
        mt.function_options,
        mt.bid_options,
        mt.search_result_view_mode
    FROM m_tenant mt
    WHERE mt.tenant_no = in_tenant_no
      AND mt.delete_flag = 0
      AND (mt.end_datetime IS NULL OR mt.end_datetime > NOW());
END;
$function$;
