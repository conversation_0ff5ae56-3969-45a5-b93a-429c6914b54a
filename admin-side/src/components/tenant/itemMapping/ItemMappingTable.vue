<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" />{{ caption }} </slot>
    </CCardHeader>
    <CCardBody>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :sorter-value="itemsSorter"
        :loading="loading"
        :items="items"
        :fields="fields"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        @update:sorter-value="sorterChange"
      >
        <template #auction_field_name="{item}">
          <td class="text-left">
            <CFormSelect
              v-if="item.editable_flag === 1"
              name="auction_field_name"
              :options="auctionItemList"
              v-model="item.field_localized_no"
              addInputClasses="w-100"
            />
            <span v-else>{{ item.auction_field_name }}</span>
          </td>
        </template>
        <template #external_system_item_key="{item}">
          <td class="text-left">
            <CFormInput
              v-if="item.editable_flag === 1"
              name="external_system_item_key"
              v-model="item.external_system_item_key"
              addInputClasses="w-100"
            />
            <span v-else>{{ item.external_system_item_key }}</span>
          </td>
        </template>

        <template #action="{item}">
          <td style="width: 100px">
            <div class="d-flex justify-content-center align-items-center gap-1">
              <CButton v-if="item.editable_flag === 1" size="sm" color="danger" @click="deleteItem(item)"
                >削除</CButton
              >
            </div>
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />

      <div>
        <CRow>
          <CCol sm="4" />
          <CCol md="2" class="text-right d-grid">
            <CButton color="primary" @click="addNewItem" block
              >行を追加</CButton
            >
          </CCol>
          <CCol col="2" class="text-right d-grid">
            <CButton color="primary" @click="update" block>更新</CButton>
          </CCol>
          <CCol sm="4" />
        </CRow>
      </div>
    </CCardBody>
  </CCard>
</template>

<script setup>
import {CDataTable, CPagination} from '@/components/Table';
import {useRouter} from 'vue-router';

  const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
    'add-new-item',
    'delete-item',
    'update',
  ]);
  const props = defineProps({
    items: Array,
    auctionItemList: Array,
    fields: {
      type: Array,
      default() {
        return [
          {
            key: 'auction_field_name',
            label: 'オークション項目名',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'external_system_item_key',
            label: '外部システム物理名',
            _classes: 'text-center',
            sorter: true,
          },
          {key: 'action', label: '操作', _classes: 'text-center'},
        ];
      },
    },
    caption: {
      type: String,
      default: '',
    },
    loading: Boolean,
    activePage: Number,
    itemsPerPage: Number,
    pages: Number,
    itemsSorter: Object,
  });

  const router = useRouter();

  const pageChange = val => {
    if (props.items.length > 0) {
      emit('page-change', val);
    }
  };

  const sorterChange = val => {
    emit('sorter-change', val);
  };

  const paginationChange = val => {
    emit('pagination-change', val);
  };

  const addNewItem = () => {
    emit('add-new-item');
  };

  const update = () => {
    emit('update');
  };

  const deleteItem = item => {
    emit('delete-item', item);
  };
</script>
