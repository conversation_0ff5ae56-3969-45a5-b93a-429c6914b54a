<script setup>
  import {computed, defineEmits, onMounted, ref, watch} from 'vue';

  const emit = defineEmits(['add-new-item']);
  const props = defineProps({
    fieldList: Array,
    resourceType: String,
    windowId: String,
    displayArea: String,
  });

  const itemData = ref({});

  const resourceList = ref([
    {value: 'item', label: '商品'},
    {value: 'member', label: '会員'},
  ]);

  const displayAreaOptions = ref([
    {value: '商品詳細（タグ）', label: '商品詳細（タグ）'},
    {value: '商品詳細（表）', label: '商品詳細（表）'},
    {value: '商品詳細（説明）', label: '商品詳細（説明）'},
  ]);

  const selectField = ref({
    resource: '',
    field_no: '',
    display_area: '',
  });

  // Error dialog state
  const isErrorDialog = ref(false);
  const errorMsg = ref([]);

  const itemNameList = computed(() => {
    return props.fieldList
      .filter(item => item.field_division === selectField.value.resource)
      .map(item => ({
        value: String(item.field_no),
        label: item.logical_name,
      }));
  });

  // Computed property to check if display area should be shown
  const shouldShowDisplayArea = computed(() => {
    return props.windowId === 'item_detail';
  });

  // Function to set default display area
  const setDefaultDisplayArea = () => {
    if (props.windowId === 'item_detail' && !selectField.value.display_area) {
      selectField.value.display_area = displayAreaOptions.value[0]?.value || '';
    }
  };

  onMounted(() => {
    selectField.value.resource = props.resourceType;
    selectField.value.display_area = props.displayArea || '';
    setDefaultDisplayArea();
  });

  watch(
    () => props.displayArea,
    newValue => {
      selectField.value.display_area = newValue || '';
      setDefaultDisplayArea();
    }
  );

  // Watch for windowId changes to set default display area
  watch(
    () => props.windowId,
    newValue => {
      if (newValue === 'item_detail') {
        setDefaultDisplayArea();
      }
    }
  );

  watch(
    () => props.fieldList,
    newFieldList => {
      if (newFieldList && newFieldList.length > 0) {
        selectPulldownValue();
      }
    }
  );

  watch(
    () => selectField.value.resource,
    newValue => {
      selectPulldownValue();
    }
  );

  const addNewItem = () => {
    // Validation for display_area when window_id is item_detail
    if (props.windowId === 'item_detail' && !selectField.value.display_area) {
      errorMsg.value = ['商品詳細ページの場合、表示エリアの選択が必要です。'];
      isErrorDialog.value = true;
      return;
    }

    const selectItem = props.fieldList.filter(
      item => String(item.field_no) === selectField.value.field_no
    );
    itemData.value = selectItem[0] ? selectItem[0] : {};
    emit('add-new-item', {
      ...itemData.value,
      display_area: selectField.value.display_area,
    });
  };

  // プルダウンの初期値設定
  const selectPulldownValue = () => {
    const options = itemNameList.value;
    selectField.value.field_no = options[0] ? String(options[0].value) : '';
  };
</script>

<template>
  <div>
    <div>
      <CForm onsubmit="return false;">
        <CRow class="mb-3">
          <CCol sm="3">
            <label>リソース</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="resource_type"
              :options="resourceList"
              v-model="selectField.resource"
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="3">
            <label>項目名</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="item_name"
              :options="itemNameList"
              v-model="selectField.field_no"
            />
          </CCol>
        </CRow>
        <CRow v-if="shouldShowDisplayArea" class="mb-3">
          <CCol sm="3">
            <label>表示エリア</label>
          </CCol>
          <CCol sm="3">
            <CFormSelect
              name="display_area"
              :options="displayAreaOptions"
              v-model="selectField.display_area"
            />
          </CCol>
        </CRow>
      </CForm>
    </div>
    <div class="mt-4">
      <CRow>
        <CCol sm="2" class="d-grid">
          <CButton size="sm" color="primary" @click="addNewItem">追加</CButton>
        </CCol>
      </CRow>
    </div>

    <!-- Error Modal -->
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
          errorMsg = [];
        }
      "
    >
      <CModalHeader>
        <CModalTitle>エラー</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="errorMsg">
          <div v-for="(val, i) in errorMsg" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            isErrorDialog = false;
            errorMsg = [];
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>
