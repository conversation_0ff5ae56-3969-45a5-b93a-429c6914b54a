<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="cil-grid" />{{ caption }} </slot>
    </CCardHeader>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :loading="loading"
        :items="items"
        :fields="tableFields"
      >
        <template #logical_name="{item}">
          <td class="text-left" style="width: 200px">
            {{ item.logical_name }}
          </td>
        </template>
        <template #physical_name="{item}">
          <td class="text-left" style="width: 150px">
            {{ item.physical_name }}
          </td>
        </template>
        <template #field_division="{item}">
          <td class="text-left" style="width: 150px">
            {{ getResourceLabel(item.field_division) }}
          </td>
        </template>
        <template #display_area="{item}">
          <td class="text-left" style="width: 150px">
            {{ item.display_area || '' }}
          </td>
        </template>
        <template #input_type="{item}">
          <td class="text-left" style="width: 150px">
            {{ getInputMethodLabel(item.input_type) }}
          </td>
        </template>
        <template #required_flag="{item}">
          <td class="text-center" style="width: 100px">
            <CFormCheck :checked="item.required_flag === 1" disabled />
          </td>
        </template>
        <template #data_type="{item}">
          <td>
            {{ getInputValidationLabel(item.data_type) }}
          </td>
        </template>
        <template #restrictions="{item}">
          <td>
            {{ getRestrictions(item) }}
          </td>
        </template>

        <template #action="{item}">
          <td style="width: 150px">
            <div class="d-flex justify-content-center align-items-center gap-1">
              <CButton size="sm" @click="moveUp(item)">
                <CIcon icon="cilArrowTop" />
              </CButton>
              <CButton size="sm" @click="moveDown(item)">
                <CIcon icon="cilArrowBottom" />
              </CButton>
              <CButton size="sm" color="danger" @click="deleteItem(item)"
                >削除</CButton
              >
            </div>
          </td>
        </template>
      </CDataTable>
    </CCardBody>

    <!-- Add new item -->
    <CCardHeader
      style="border-top: 1px solid; border-bottom: none; border-color: #d8dbe0"
    >
      <slot name="header"><strong>追加項目選択</strong></slot>
    </CCardHeader>
    <CCardBody>
      <AddNewItem
        :resourceType="'item'"
        :fieldList="fieldList"
        :windowId="windowId"
        :displayArea="displayArea"
        @add-new-item="addNewItem"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table';
import {computed, defineEmits, defineProps, ref} from 'vue';
import AddNewItem from './AddNewItem.vue';
const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
    'add-new-item',
    'delete-item',
    'move-up',
    'move-down',
  ]);
  const props = defineProps({
    items: Array,
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fieldList: Array,
    fields: {
      type: Array,
      default() {
        return [];
      },
    },
    caption: {
      type: String,
      default: '',
    },
    loading: Boolean,
    windowId: String,
    displayArea: String,
  });

  const inputValidationOptions = ref([
    {value: 'constant', label: '定数'},
    {value: 'character', label: '文字'},
    {value: 'integer', label: '数値（小数なし）'},
    {value: 'float', label: '数値（小数あり）'},
    {value: 'reg', label: '正規表現'},
  ]);

  const inputMethodOptions = ref([
    {value: 'pulldown', label: 'プルダウン'},
    {value: 'text', label: 'テキスト'},
    {value: 'checkbox', label: 'チェックボックス'},
    {value: 'file', label: 'ファイル'},
  ]);

  const constResourceList = ref([
    {value: 'item', label: '商品'},
    {value: 'member', label: '会員'},
  ]);

  // Dynamic fields based on windowId
  const tableFields = computed(() => {
    const baseFields = [
      {
        key: 'logical_name',
        label: '項目名',
        _classes: 'text-center',
        sorter: false,
      },
      {
        key: 'physical_name',
        label: '物理名',
        _classes: 'text-center',
        sorter: false,
      },
      {
        key: 'field_division',
        label: '項目区分',
        _classes: 'text-center',
        sorter: false,
      },
      {
        key: 'input_type',
        label: '入力方法',
        _classes: 'text-center',
        sorter: false,
      },
      {
        key: 'required_flag',
        label: '必須',
        _classes: 'text-center',
        sorter: false,
      },
      {
        key: 'data_type',
        label: '入力制限',
        _classes: 'text-center',
        sorter: false,
      },
      {
        key: 'restrictions',
        label: '制限内容',
        _classes: 'text-center',
        sorter: false,
      },
    ];

    // Add display_area column for item_detail
    if (props.windowId === 'item_detail') {
      baseFields.splice(2, 0, {
        key: 'display_area',
        label: '表示エリア',
        _classes: 'text-center',
        sorter: false,
      });
    }

    baseFields.push({
      key: 'action',
      label: 'アクション',
      _classes: 'text-center',
      sorter: false,
    });

    return props.fields && props.fields.length > 0 ? props.fields : baseFields;
  });

  const getResourceLabel = id => {
    const option = constResourceList.value.find(
      item => String(item.value) === String(id)
    );
    return option ? option.label : '';
  };
  const getInputValidationLabel = id => {
    const option = inputValidationOptions.value.find(
      item => String(item.value) === String(id)
    );
    return option ? option.label : '';
  };
  const getInputMethodLabel = id => {
    const option = inputMethodOptions.value.find(
      item => String(item.value) === String(id)
    );
    return option ? option.label : '';
  };
  const getRestrictions = item => {
    let restrictions = '';
    if (item.data_type === 'constant') {
      restrictions = item.input_data_list
        ? item.input_data_list.key_string
        : '';
    } else if (item.data_type === 'character') {
      restrictions = item.max_length ? String(item.max_length) : '';
    } else if (item.data_type === 'integer' || item.data_type === 'float') {
      restrictions = item.max_value ? String(item.max_value) : '';
    } else if (item.data_type === 'reg') {
      restrictions = item.regular_expressions ? item.regular_expressions : '';
    } else {
      restrictions = '';
    }

    return restrictions;
  };

  const addNewItem = item => {
    emit('add-new-item', item);
  };

  const deleteItem = item => {
    emit('delete-item', item);
  };

  const moveUp = item => {
    emit('move-up', item);
  };

  const moveDown = item => {
    emit('move-down', item);
  };
</script>
