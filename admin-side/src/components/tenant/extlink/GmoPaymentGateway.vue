<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>GMO Payment Gateway</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              ショップID
            </CCol>
            <CCol sm="12">
              <CFormInput name="shop_id" v-model="gmoShop.shop_id" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              ショップパスワード
            </CCol>
            <CCol sm="12">
              <CFormInput
                name="shop_password"
                v-model="gmoShop.shop_password"
                type="password"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left"> サイトID </CCol>
            <CCol sm="12">
              <CFormInput name="site_id" v-model="gmoShop.site_id" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="12" class="d-flex justify-content-left">
              サイトパスワード
            </CCol>
            <CCol sm="12">
              <CFormInput
                name="site_password"
                v-model="gmoShop.site_password"
                type="password"
              />
            </CCol>
          </CRow>
        </CForm>
        <CRow class="mt-3">
          <CCol sm="5" />
          <CCol sm="2" class="d-grid">
            <CButton color="primary" @click="update">更新</CButton>
          </CCol>
          <CCol sm="5" />
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script setup>
  import {defineEmits, ref, watch, computed } from 'vue';

  const emit = defineEmits(['update', 'passwordError'])

  const props = defineProps({
    init: { type: Object, default: () => ({}) }
  });

  const gmoShop = ref({ ...props.init });
  const extLinkNo = ref(1);
  const passwordError = ref([]);

  watch(
    () => props.init,
    (newVal) => {
      gmoShop.value = { ...newVal };
    },
    { immediate: true }
  );

  // パスワード要件チェック
  const shopHasNumber = computed(() => /\d/.test(gmoShop.value.shop_password || ''));
  const shopHasLowerCase = computed(() => /[a-z]/.test(gmoShop.value.shop_password || ''));
  const shopHasUpperCase = computed(() => /[A-Z]/.test(gmoShop.value.shop_password || ''));
  const shopHasSymbol = computed(() => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(gmoShop.value.shop_password || ''));
  const siteHasNumber = computed(() => /\d/.test(gmoShop.value.site_password || ''));
  const siteHasLowerCase = computed(() => /[a-z]/.test(gmoShop.value.site_password || ''));
  const siteHasUpperCase = computed(() => /[A-Z]/.test(gmoShop.value.site_password || ''));
  const siteHasSymbol = computed(() => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(gmoShop.value.site_password || ''));

// パスワードバリデーション関数
  const validatePassword = () => {
    const password = gmoShop.value.password || '';
    passwordError.value = [];

    // ショップパスワードのチェック
    const shopPassword = gmoShop.value.shop_password || '';
    if (shopPassword) {
      // if (shopPassword.length < 8) {
      //   passwordError.value.push('ショップパスワードは8文字以上で入力してください');
      // }
      if (!shopHasNumber.value) {
        passwordError.value.push('ショップパスワードに半角数字を含めてください');
      }
      if (!shopHasLowerCase.value) {
        passwordError.value.push('ショップパスワードに半角英小文字を含めてください');
      }
      if (!shopHasUpperCase.value) {
        passwordError.value.push('ショップパスワードに半角英大文字を含めてください');
      }
      if (!shopHasSymbol.value) {
        passwordError.value.push('ショップパスワードに記号を含めてください');
      }
    }

    // サイトパスワードのチェック
    const sitePassword = gmoShop.value.site_password || '';
    if (sitePassword) {
      // if (sitePassword.length < 8) {
      //   passwordError.value.push('サイトパスワードは8文字以上で入力してください');
      // }
      if (!siteHasNumber.value) {
        passwordError.value.push('サイトパスワードに半角数字を含めてください');
      }
      if (!siteHasLowerCase.value) {
        passwordError.value.push('サイトパスワードに半角英小文字を含めてください');
      }
      if (!siteHasUpperCase.value) {
        passwordError.value.push('サイトパスワードに半角英大文字を含めてください');
      }
      if (!siteHasSymbol.value) {
        passwordError.value.push('サイトパスワードに記号を含めてください');
      }
    }
  };

  const update = () => {
    console.log('update');

    validatePassword();

    if (passwordError.value.length > 0) {
      emit(
        'passwordError',
        { ...passwordError.value, },
      )
      return;
    }

    emit(
      'update',
      { ...gmoShop.value, },
      extLinkNo.value
    )
  };
</script>
