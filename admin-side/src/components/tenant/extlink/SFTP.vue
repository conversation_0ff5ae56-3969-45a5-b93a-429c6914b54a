<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>SFTP</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> SFTPのURL </CCol>
            <CCol sm="10">
              <CFormInput name="url" v-model="gmoShop.url" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> ユーザーID </CCol>
            <CCol sm="10">
              <CFormInput name="id" v-model="gmoShop.id" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> パスワード </CCol>
            <CCol sm="10">
              <CFormInput
                name="secret_access_key"
                v-model="gmoShop.password"
                type="password"
              />
            </CCol>
          </CRow>
        </CForm>
        <CRow>
          <CCol sm="5" />
          <CCol sm="2" class="d-grid">
            <CButton color="primary" @click="update">更新</CButton>
          </CCol>
          <CCol sm="5" />
        </CRow>
      </CCardBody>
    </CCard>
  </div>
</template>

<script setup>
  import {defineEmits, ref, watch, computed } from 'vue';

  const emit = defineEmits(['update', 'passwordError']);

  const props = defineProps({
    init: { type: Object, default: () => ({}) }
  });

  const gmoShop = ref({ ...props.init });
  const extLinkNo = ref(3);
  const passwordError = ref([]);

  watch(
    () => props.init,
    (newVal) => {
      gmoShop.value = { ...newVal };
    },
    { immediate: true }
  );

  // パスワード要件チェック
  const hasNumber = computed(() => /\d/.test(gmoShop.value.password || ''));
  const hasLowerCase = computed(() => /[a-z]/.test(gmoShop.value.password || ''));
  const hasUpperCase = computed(() => /[A-Z]/.test(gmoShop.value.password || ''));
  const hasSymbol = computed(() => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(gmoShop.value.password || ''));

// パスワードバリデーション関数
  const validatePassword = () => {
    const password = gmoShop.value.password || '';
    passwordError.value = [];

    // if (password.length < 8) {
    //   passwordError.value = 'パスワードは8文字以上で入力してください';
    //   return;
    // }

    if (!hasNumber.value) {
      passwordError.value.push('パスワードに半角数字を含めてください');
    }

    if (!hasLowerCase.value) {
      passwordError.value.push('パスワードに半角英小文字を含めてください');
    }

    if (!hasUpperCase.value) {
      passwordError.value.push('パスワードに半角英大文字を含めてください');
    }

    if (!hasSymbol.value) {
      passwordError.value.push('パスワードに記号を含めてください');
    }
  };

  const update = () => {
    console.log('update');

    validatePassword();

    if (passwordError.value.length > 0) {
      emit(
        'passwordError',
        { ...passwordError.value, },
      )
      return;
    }

    emit(
      'update',
      { ...gmoShop.value, },
      extLinkNo.value
    )
  };
</script>
