<script setup>
  import {defineEmits, defineProps, onMounted, ref} from 'vue';
const emit = defineEmits(['confirm'])
const props = defineProps({
  joinMemberNos: {
    type: Array,
    default: () => []
  },
  allMembers: {
    type: Array,
    default: () => []
  },
})
const joinMemberNos = props.joinMemberNos
console.log('joinMemberNos', joinMemberNos)
  const open = ref(false);
  const allMembers = ref([]);
  const selectedMembers = ref([]);
  const searchKey = ref('');
  onMounted(async () => {
    console.log('props.allMembers', props.allMembers)
    allMembers.value = props.allMembers
    selectedMembers.value = allMembers.value.filter(member => joinMemberNos.includes(String(member.member_no)))
  })
  const addMembers = () => {
    allMembers.value.forEach(member => {
      if (member.active && !selectedMembers.value.includes(member)) {
        member.active = false
        selectedMembers.value.push(member)
      }
    })
  }
  const deleteMembers = () => {
    selectedMembers.value = selectedMembers.value.filter(row => {
      const active = row.active
      row.active = false
      return !active
    })
  }
  const registExhibitionMember = () => {
    emit('confirm', selectedMembers.value)
  }
</script>
<template>
  <CButton
    color="primary"
    @click="
      () => {
        open = true;
      }
    "
  >
    会員選択
  </CButton>
  <CModal
    alignment="center"
    scrollable
    :visible="open"
    @close="
      () => {
        open = false;
      }
    "
    size="lg"
  >
    <CModalHeader>
      <CModalTitle id="VerticallyCenteredExample2">会員選択</CModalTitle>
    </CModalHeader>
    <CModalBody>
      <div class="d-flex">
        <CCard style="width: 30rem; max-height: 67vh;overflow: auto;">
          <CCardBody>
            <CVirtualScroller :visibleItems="5">
              <CListGroup>
                <CListGroupItem style="cursor: pointer;" @click="mem.active = !mem.active" :active="mem.active" v-for="mem in selectedMembers">
                  <CIcon icon="cil-user" />
                  <span>{{ mem.member_id }}</span>
                </CListGroupItem>
              </CListGroup>
            </CVirtualScroller>
          </CCardBody>
        </CCard>
        <div
          style="width: 8rem"
          class="mx-1 d-flex flex-column justify-content-center align-items-center gap-2"
        >
          <div class="w-100 d-grid">
            <CButton color="success" @click="addMembers">
              <CIcon icon="cil-caret-left" />追加
            </CButton>
          </div>
          <div class="w-100 d-grid">
            <CButton color="danger" @click="deleteMembers"
              >削除
              <CIcon icon="cil-caret-right" />
            </CButton>
          </div>
        </div>
        <div style="width: 30rem" class="d-flex flex-column gap-2">
          <div class="d-flex gap-1">
            <CFormInput placeholder="会員名..."  v-model="searchKey"/>
            <CButton
              type="submit"
              color="primary"
              class="col-auto"
              @click="() => {}"
              >検索</CButton
            >
          </div>
          <CCard style="max-height: 60vh;overflow: auto;">
            <CCardBody>
              <CVirtualScroller :visibleItems="5">
                <CListGroup>
                <CListGroupItem style="cursor: pointer;" @click="mem.active = !mem.active" :active="mem.active" v-for="mem in allMembers.filter(member => !selectedMembers.includes(member) && (!searchKey || member.member_id.includes(searchKey)))">
                  <CIcon icon="cil-user" />
                  <span>{{ mem.member_id }}</span>
                </CListGroupItem>
              </CListGroup>
              </CVirtualScroller>
            </CCardBody>
          </CCard>
        </div>
      </div>
    </CModalBody>
    <CModalFooter>
      <CButton
        color="secondary"
        @click="
          () => {
            open = false;
          }
        "
      >
        閉じる
      </CButton>
      <CButton
        color="primary"
        @click="
          () => {
            registExhibitionMember()
            open = false;
          }
        "
        >確認</CButton
      >
    </CModalFooter>
  </CModal>
</template>
