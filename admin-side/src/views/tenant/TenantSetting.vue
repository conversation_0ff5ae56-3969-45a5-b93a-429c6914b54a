<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>テナント設定</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> テナント名 </CCol>
            <CCol sm="10">
              <CFormInput name="tenant_name" v-model="tenant.tenant_name" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 運営者名 </CCol>
            <CCol sm="10">
              <CFormInput name="operator_name" v-model="tenant.operator_name" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> メールアドレス </CCol>
            <CCol sm="10">
              <CFormInput name="contact" v-model="tenant.contact" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 認証 </CCol>
            <CCol sm="10">
              <CFormCheck
                id="mfa_required"
                label="会員の２段階認証を必須とする"
                v-model="tenant.mfa_required"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 通貨 </CCol>
            <CCol sm="2">
              <CFormSelect
                name="currency"
                :options="currencyOptions"
                v-model="tenant.currency"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 言語 </CCol>
            <CCol sm="10">
              <CFormCheck
                v-for="lang in languageOptions"
                :key="lang.value"
                inline
                :id="lang.value"
                :value="lang.value"
                :label="lang.label"
                v-model="tenant.language"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 商品検索結果の表示 </CCol>
            <CCol sm="10">
              <CFormCheck
                type="radio"
                inline
                v-for="searchResult in searchResultOptions"
                :key="searchResult.value"
                :value="searchResult.value"
                :label="searchResult.label"
                :custom="true"
                :id="`search_result_${searchResult.value}`"
                name="search_result_view_mode"
                v-model="tenant.search_result_view_mode"
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CRow class="mb-3">
        <CCol sm="5" />
        <CCol sm="2" class="d-grid">
          <CButton @click="validOrUpdateTenantSetting" color="primary"
            >更新</CButton
          >
        </CCol>
        <CCol sm="5" />
      </CRow>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="errorMsg">
          <div v-for="(val, i) in errorMsg" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="isErrorDialog = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="validateModal"
      @close="
        () => {
          validateModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{ validateModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          v-if="validateResult.length > 0"
          @click="validateModal = false"
          color="dark"
          :disabled="loading"
          >閉じる
        </CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isConfirmDialog"
      @close="
        () => {
          isConfirmDialog = false;
          errorMsg = '';
          errorStatus = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>登録確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!errorStatus">この内容で保存してもよろしいですか？</div>
        <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              isConfirmDialog = false;
              errorMsg = '';
              errorStatus = false;
            }
          "
          :disabled="loading"
          color="dark"
        >
          <div v-if="errorStatus">OK</div>
          <div v-if="!errorStatus">キャンセル</div>
        </CButton>
        <CButton
          v-if="!errorStatus"
          @click="updateCreditRemaining"
          :disabled="loading"
          :loading="loading"
          color="primary"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="successModal"
      @close="
        () => {
          successModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="reloadPage" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
          btnClicked = false;
          next(false);
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{
          (selectStatus ? '登録' : '編集') + '中止確認'
        }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              cancelModal = false;
              btnClicked = false;
              next(false);
            }
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton
          @click="
            () => {
              cancelModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="failModal"
      @close="
        () => {
          failModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>失敗</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>更新エラーが発生しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="failModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
    <CElementCover v-if="loading" :opacity="0.8" />
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
import Base from '@/common/base';
import {CElementCover, ScaleLoader} from '@/components/Table';
import {useCommonStore} from '@/store/common';
import {onMounted, ref} from 'vue';
import {useRoute, useRouter} from 'vue-router';

  const route = useRoute();
  const router = useRouter();
  const store = useCommonStore();

  const loading = ref(true);

  // CSV用
  const validateResult = ref([]);

  // dialog
  const isErrorDialog = ref(false);
  const isConfirmDialog = ref(false);
  const cancelModal = ref(false);
  const successModal = ref(false);
  const failModal = ref(false);

  // Validate input
  const validateModal = ref(false);
  const validateModalTitle = ref('確認');

  const errorMsg = ref([]);

  const forceExit = ref(false);
  // Confirm mode
  const confirmMode = ref(false);

  const tenant = ref({
    tenant_name: '',
    operator_name: '',
    contact: '',
    mfa_required: false,
    currency: '',
    language: [],
    search_result_view_mode: 'NOT_SELECTED',  // 初期値を設定
  });

  const currencyOptions = [
    {value: 'JPY', label: 'JPY'},
    {value: 'USD', label: 'USD'},
    {value: 'EUR', label: 'EUR'},
  ];
  const languageOptions = ref([]);
  const searchResultOptions = [
    {value: 'panel', label: '入札欄無し・表示切り替えあり'},
    {value: 'row', label: '入札欄あり'},
  ];

  onMounted(() => {
    loading.value = true;
    const request = {
      key_strings: ['LANGUAGE_CODE'],
    };
    Methods.apiExecute('get-constants-by-keys', request)
      .then(response => {
        if (response.status === 200) {
          console.log('constant: ', response.data);
          languageOptions.value = [];
          for (const constant of response.data) {
            switch (constant.key_string) {
              case 'LANGUAGE_CODE':
                languageOptions.value.push({
                  value: constant.value1,
                  label: constant.value2,
                });
                break;
              default:
                break;
            }
          }
          Methods.apiExecute('get-tenant-setting').then(
            response => {
              if (response.status === 200) {
                console.log('get-tenant-setting response', response);
                tenant.value.tenant_name = response.data[0].tenant_name;
                tenant.value.operator_name = response.data[0].company_name;
                tenant.value.contact = response.data[0].contact_email;
                tenant.value.mfa_required = response.data[0].two_factor_auth_required === 1;  // Booleanに変換
                tenant.value.currency = response.data[0].currency_code || 'jp';
                tenant.value.language = response.data[0].language_code_list.filter(lang =>
                  languageOptions.value.some(option => option.value === lang)
                );
                tenant.value.search_result_view_mode = response.data[0].search_result_view_mode || 'NOT_SELECTED';
                console.log('tenant.value', tenant.value);
              }
            }
          );
        }
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        validateResult.value = Methods.parseHtmlResponseError(router, error);
      });
    loading.value = false;
  });

  const validOrUpdateTenantSetting = async () => {
    console.log('validOrUpdateTenantSetting');

    loading.value = true;

    // Email delivery flag
    const tenant_data = JSON.parse(JSON.stringify(tenant.value));
    console.log('tenant', tenant.value);
    console.log('tenant_data', tenant_data);
    tenant_data.mfa_required = tenant.value.mfa_required ? 1 : 0;
    tenant_data.search_result_view_mode = tenant.value.search_result_view_mode === 'NOT_SELECTED' ? null : tenant.value.search_result_view_mode;

    // エラーチェック、不要なパラメータ削除
    paramCheck();

    if (errorMsg.value.length > 0) {
      loading.value = false;
      isErrorDialog.value = true;
    } else {
      await Methods.apiExecute('update-tenant-setting', tenant_data)
        .then(response => {
          if (response.status === 200) {
            // 成功時の処理
            successModal.value = true;
            loading.value = false;
          }
        })
        .catch(error => {
          loading.value = false;
          successModal.value = false;
          isErrorDialog.value = true;
          errorMsg.value = Methods.parseHtmlResponseError(router, error);
        });
    }
  };

  const paramCheck = () => {
    errorMsg.value = [];
    if (!tenant.value.tenant_name || tenant.value.tenant_name.length === 0) {
      errorMsg.value.push('テナント名は必須です。');
    }
    if (
      !tenant.value.operator_name ||
      tenant.value.operator_name.length === 0
    ) {
      errorMsg.value.push('運営者名は必須です。');
    }
    if (!tenant.value.contact || tenant.value.contact.length === 0) {
      errorMsg.value.push('メールアドレスは必須です。');
    }
    if (!tenant.value.currency || tenant.value.currency.length === 0) {
      errorMsg.value.push('通貨は必須です。');
    }
    if (!tenant.value.language || tenant.value.language.length === 0) {
      errorMsg.value.push('言語は必須です。');
    }
    if (tenant.value.search_result_view_mode === 'NOT_SELECTED') {
      errorMsg.value.push('商品検索結果の表示は必須です。');
    }
  };

  const backButton = () => {
    if (confirmMode.value) {
      // Back to member edit mode
      confirmMode.value = false;
      Base.scrollToTop();
    } else {
      // Back to member list
      router.push({path: '/members'});
    }
  };

  const reloadPage = event => {
    console.log('reloadPage');
    successModal.value = false;
    router.go(0);
  };
</script>
