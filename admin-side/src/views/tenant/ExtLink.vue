<script setup>
  import {onMounted, ref} from 'vue';
  import GmoCloudEC from '../../components/tenant/extlink/GmoCloudEC.vue';
  import GmoPaymentGateway from '../../components/tenant/extlink/GmoPaymentGateway.vue';
  import SFTP from '../../components/tenant/extlink/SFTP.vue';
  import ShareThis from '../../components/tenant/extlink/ShareThis.vue';
  import Methods from '@/api/methods';
  import {useRoute, useRouter} from 'vue-router';
  import {CElementCover} from '@/components/Table';
  const tabPanePillsActiveKey = ref(1);

  const loading = ref(true);
  const validateResult = ref([]);
  const route = useRoute();
  const router = useRouter();

  // dialog
  const isErrorDialog = ref(false);
  const isConfirmDialog = ref(false);
  const cancelModal = ref(false);
  const successModal = ref(false);
  const failModal = ref(false);

  const tempExtLinkOptions = ref(null);
  const tempExtLinkNo = ref(null);

  // 各タブの初期値
  const gmoPaymentGatewayInit = ref({
    shop_id: '',
    shop_password: '',
    site_id: '',
    site_password: '',
  });
  const gmoCloudECInit = ref({
    url: '',
    access_key: '',
    secret_access_key: '',
  });
  const sftpInit = ref({
    url: '',
    id: '',
    password: '',
  });
  const shareThisInit = ref({
    script: '',
  });

  onMounted(async () => {
    // Any initialization logic can go here
    // ここでAPIから初期値を取得する
    loading.value = true;
    Methods.apiExecute('get-ext-link-options')
      .then(response => {
        if (response.status === 200) {
          // resourceList.value = response.data.filter(
          //   item => item.bid_limit_flag === 1
          // );
          response.data.forEach(item => {
            // ext_link_optionsがnull/undefined/空オブジェクトならスキップ
            if (!item.ext_link_options || Object.keys(item.ext_link_options).length === 0) {
              return;
            }
            switch (item.ext_link_no) {
              case 1:
                gmoPaymentGatewayInit.value = item.ext_link_options;
                break;
              case 2:
                gmoCloudECInit.value = item.ext_link_options;
                break;
              case 3:
                sftpInit.value = item.ext_link_options;
                break;
              case 4:
                shareThisInit.value = item.ext_link_options;
                break;
            }
          });
          console.log('response.data', response.data);
        }
        loading.value = false;
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        validateResult.value = Methods.parseHtmlResponseError(router, error);
      });
  });

  const upsertConfirm = (extLinkOptions, extLinkNo) => {
    tempExtLinkOptions.value = extLinkOptions;
    tempExtLinkNo.value = extLinkNo;

    isConfirmDialog.value = true;
  }

  const upsertExtLink = () => {
    console.log('upsertExtLink', tempExtLinkOptions.value);
    console.log('extLinkNo', tempExtLinkNo.value);

    // モーダル系のフラグ
    isConfirmDialog.value = false;
    loading.value = true;

    const params = {
      ext_link_no: tempExtLinkNo.value,
      ext_link_options: tempExtLinkOptions.value
    };

    Methods.apiExecute('upsert-ext-link-options', params)
      .then(response => {
        console.log(`response: ${JSON.stringify(response)}`);
        isConfirmDialog.value = false;
        loading.value = false;
        if (response.status === 200) {
          successModal.value = true;
        } else {
          failModal.value = true;
        }
      })
      .catch(error => {
        loading.value = false;
        isConfirmDialog.value = false;
        if (
          error.response &&
          (error.response.status === 400 || error.response.status === 409)
        ) {
          failModal.value = true;
        }
        Methods.parseHtmlResponseError(router, error);
      });
  };

  const handlePasswordError = passwordError => {
    console.log('handlePasswordError', passwordError);
    isConfirmDialog.value = false;
    isErrorDialog.value = true;
    validateResult.value = passwordError;
  };

  const reloadPage = event => {
    console.log('reloadPage');
    successModal.value = false;
    router.go(0);
  };
</script>
<template>
  <CCard class="text-center mb-3">
    <CCardHeader>
      <!-- <span>外部連携管理</span> -->
      <CNav variant="tabs" class="card-header-tabs">
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 1"
            @click="
              () => {
                tabPanePillsActiveKey = 1;
              }
            "
          >
            GMO PaymentGateway
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 2"
            @click="
              () => {
                tabPanePillsActiveKey = 2;
              }
            "
          >
            GMO クラウドEC
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 3"
            @click="
              () => {
                tabPanePillsActiveKey = 3;
              }
            "
          >
            SFTP
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 4"
            @click="
              () => {
                tabPanePillsActiveKey = 4;
              }
            "
          >
            ShareThis
          </CNavLink>
        </CNavItem>
      </CNav>
    </CCardHeader>
    <CCardBody>
      <CTabContent>
        <CTabPane
          role="tabpanel"
          aria-labelledby="home-tab"
          :visible="tabPanePillsActiveKey === 1"
        >
          <GmoPaymentGateway
            :init="gmoPaymentGatewayInit"
            @update="upsertConfirm"
            @passwordError="handlePasswordError"
          />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 2"
        >
          <GmoCloudEC
            :init="gmoCloudECInit"
            @update="upsertConfirm"
          />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 3"
        >
          <SFTP
            :init="sftpInit"
            @update="upsertConfirm"
            @passwordError="handlePasswordError"
          />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 4"
        >
          <ShareThis
            :init="shareThisInit"
            @update="upsertConfirm"
          />
        </CTabPane>
      </CTabContent>
    </CCardBody>
  </CCard>

  <CModal
    backdrop="static"
    :keyboard="false"
    :visible="isErrorDialog"
    @close="
      () => {
        isErrorDialog = false;
      }
    "
  >
    <CModalHeader>
      <CModalTitle>確認</CModalTitle>
    </CModalHeader>
    <CModalBody closeButton>
      <div v-if="validateResult">
        <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
      </div>
    </CModalBody>
    <CModalFooter>
      <CButton @click="isErrorDialog = false" color="dark">閉じる</CButton>
    </CModalFooter>
  </CModal>

  <CModal
    backdrop="static"
    :keyboard="false"
    :visible="isConfirmDialog"
    @close="
      () => {
        isConfirmDialog = false;
        errorMsg = '';
        errorStatus = false;
      }
    "
  >
    <CModalHeader>
      <CModalTitle>登録確認</CModalTitle>
    </CModalHeader>
    <CModalBody>
      <div v-if="!errorStatus">この内容で保存してもよろしいですか？</div>
      <div v-for="text in errorMsg" :key="text">{{ text }}</div>
    </CModalBody>
    <CModalFooter>
      <CButton
        @click="
          () => {
            isConfirmDialog = false;
            errorMsg = '';
            errorStatus = false;
          }
        "
        :disabled="loading"
        color="dark"
      >
        <div v-if="errorStatus">OK</div>
        <div v-if="!errorStatus">キャンセル</div>
      </CButton>
      <CButton
        v-if="!errorStatus"
        @click="upsertExtLink"
        :disabled="loading"
        :loading="loading"
        color="primary"
        >OK</CButton
      >
    </CModalFooter>
  </CModal>

  <CModal
    backdrop="static"
    :keyboard="false"
    :visible="successModal"
    @close="
      () => {
        successModal = false;
      }
    "
  >
    <CModalHeader>
      <CModalTitle>完了</CModalTitle>
    </CModalHeader>
    <CModalBody>
      <div>処理が完了しました。</div>
    </CModalBody>
    <CModalFooter>
      <CButton @click="reloadPage" color="dark">閉じる</CButton>
    </CModalFooter>
  </CModal>
  <CModal
    backdrop="static"
    :keyboard="false"
    :visible="failModal"
    @close="
      () => {
        failModal = false;
      }
    "
  >
    <CModalHeader>
      <CModalTitle>失敗</CModalTitle>
    </CModalHeader>
    <CModalBody>
      <div>更新エラーが発生しました。</div>
    </CModalBody>
    <CModalFooter>
      <CButton @click="failModal = false" color="dark">閉じる</CButton>
    </CModalFooter>
  </CModal>
  <CElementCover v-if="loading" :opacity="0.8" />
</template>
