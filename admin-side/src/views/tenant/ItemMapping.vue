<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> 外部システム </CCol>
            <CCol sm="5">
              <CFormSelect
                name="external_system"
                :options="extSystemOptions"
                v-model="search_condition.extSystem"
                class="form-group col-sm-3 pl-0"
                addInputClasses="w-100"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 対象 </CCol>
            <CCol sm="5" class="form-inline">
              <CFormSelect
                name="object"
                :options="objectOptions"
                v-model="search_condition.object"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="2"> 言語 </CCol>
            <CCol sm="5" class="form-inline">
              {{ language }}
            </CCol>
          </CRow>
        </CForm>

        <CRow class="align-items-center mt-4">
          <CCol sm="5"></CCol>
          <CCol sm="2" class="mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="1" />
          <CCol sm="2"></CCol>
          <CCol sm="2"></CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CRow>
      <CCol sm="12">
        <ItemTable
          name="resourceList"
          :items="resourceList"
          :auctionItemList="fieldList"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          caption="項目一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
          @add-new-item="addNewItem"
          @delete-item="deleteItem"
          @update="confirmModal = true"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="isErrorDialog"
      @close="
        () => {
          isErrorDialog = false;
          validateResult = [];
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody closeButton>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="isErrorDialog = false; validateResult = []" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="confirmModal"
      @close="
        () => {
          confirmModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>この内容で保存してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="confirmModal = false" color="dark">キャンセル</CButton>
        <CButton @click="update" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader :closeButton="false">
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              compModal = false;
            }
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
import Base from '@/common/base';
import {useCommonStore} from '@/store/common';
import {computed, onMounted, ref, watch} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import ItemTable from '../../components/tenant/itemMapping/ItemMappingTable.vue';

  const route = useRoute();
  const router = useRouter();
  const store = useCommonStore();

  const loading = ref(true);
  const constantList = ref([]);
  // 検索条件
  const objectOptions = ref([]);
  const extSystemOptions = ref([]);

  // Screen params
  const resourceList = ref([]);
  const search_condition = ref({
    object: '',
    extSystem: '',
  });
  const language = ref('');
  const displayLanguageCode = ref('');
  const activePage = ref(1);
  const itemsPerPage = ref(10);
  const pages = ref(1);
  const itemsSorter = ref({});
  const deleteResourceList = ref([]);

  // Counting
  const current_count = ref(0);
  const total_count = ref(0);

  // エラー、バリデーション用
  const validateResult = ref([]);

  // Error dialog
  const isErrorDialog = ref(false);

  // 確認ダイアログ
  const confirmModal = ref(false);

  // 完了ダイアログ
  const compModal = ref(false);

  // 言語
  const languageList = ref([]);

  // 項目リスト
  const fieldList = ref([]);

  onMounted(() => {
    getConstants()
      .then(() => {
        getResourceList()
          .then(postage => {
            resourceList.value = postage;

            itemsPerPage.value = store.itemsPerPage;
            pages.value =
              parseInt(resourceList.value.length / itemsPerPage.value, 10) +
              (resourceList.value.length % itemsPerPage.value > 0 ? 1 : 0);
            activePage.value = 1;
            pageChange(1);
            router.push({query: {page: activePage.value}}).catch(() => {});
          })
          .catch(error => {
            console.log(error);
            loading.value = false;
            isErrorDialog.value = true;
            validateResult.value = Methods.parseHtmlResponseError(
              router,
              error
            );
          });
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        isErrorDialog.value = true;
        validateResult.value = Methods.parseHtmlResponseError(router, error);
      });
  });

  watch(
    () => route.query,
    query => {
      if (query?.page) {
        activePage.value = Number(query.page);
      }
    }
  );

  watch(
    () => search_condition,
    newVal => {
      store.set(['resourceSearchCondition', newVal]);
    }
  );

  watch(
    () => search_condition.value.extSystem,
    newVal => {
      const targetObject = extSystemOptions.value.find(
        item => String(item.value) === String(newVal)
      );
      displayLanguageCode.value = targetObject
        ? targetObject.language_code
        : '';
      language.value = getLanguageName(displayLanguageCode.value);
    }
  );

  watch(
    () => itemsPerPage,
    newVal => {
      if (resourceList.value.length > newVal) {
        pages.value =
          parseInt(resourceList.value.length / newVal, 10) +
          (resourceList.value.length % newVal > 0 ? 1 : 0);
      } else {
        pages.value = 1;
      }
    }
  );

  const totalCount = computed(() => {
    return Base.number2string(total_count.value);
  });
  const currentCount = computed(() => {
    return Base.number2string(current_count.value);
  });

  const getConstants = () => {
    loading.value = true;
    return Methods.apiExecute('get-constants-by-keys-language', {
      key_strings: ['TENANT_OBJECT', 'EXT_SYSTEM', 'LANGUAGE_CODE'],
    }).then(response => {
      if (response.status === 200) {
        constantList.value = response.data;

        for (const constant of constantList.value) {
          switch (constant.key_string) {
            case 'TENANT_OBJECT':
              objectOptions.value.push({
                label: constant.value1,
                value: constant.value1,
              });
              break;
            case 'EXT_SYSTEM':
              extSystemOptions.value.push({
                label: constant.value1,
                value: constant.constant_localized_no,
                language_code: constant.language_code,
              });
              break;
            case 'LANGUAGE_CODE':
              languageList.value.push({
                value: constant.value1,
                label: constant.value2,
              });
              break;
            default:
              break;
          }
        }
        // プルダウンに初期値を設定
        if (objectOptions.value.length > 0) {
          search_condition.value.object = objectOptions.value[0].value;
        }
        if (extSystemOptions.value.length > 0) {
          search_condition.value.extSystem = String(extSystemOptions.value[0].value);
        }
        return Promise.resolve();
      }
      return Promise.resolve();
    });
  };

  const getFieldList = () => {
    const param = {
      language_code: displayLanguageCode.value,
    };
    fieldList.value = [];
    return Methods.apiExecute('get-field-all-list', param).then(response => {
      if (response.status === 200) {
        response.data.data.forEach(field => {
          fieldList.value.push({
            value: String(field.field_localized_no),
            label: field.logical_name + ' (' + field.physical_name + ')',
          });
        });
      }
      return Promise.resolve();
    });
  }

  const search = () => {
    isErrorDialog.value = false;
    loading.value = true;
    validateResult.value = [];
    if (
      search_condition.value.object === '' &&
      search_condition.value.extSystem === ''
    ) {
      loading.value = false;
      validateResult.value = ['1つ以上の条件を選択してください。'];
      isErrorDialog.value = true;
      return;
    }
    getResourceList()
      .then(data => {
        resourceList.value = data;

        pages.value =
          parseInt(resourceList.value.length / itemsPerPage.value, 10) +
          (resourceList.value.length % itemsPerPage.value > 0 ? 1 : 0);
        activePage.value = 1;
        sorterChange({});
      })
      .catch(error => {
        loading.value = false;
        isErrorDialog.value = true;
        validateResult.value = Methods.parseHtmlResponseError(router, error);
      });
  };

  const getResourceList = () => {
    total_count.value = 0;
    current_count.value = 0;
    resourceList.value = [];

    // 数値チェック
    const externalSystemNo = Number(search_condition.value.extSystem);
    if (isNaN(externalSystemNo)) {
      loading.value = false;
      isErrorDialog.value = true;
      validateResult.value = ['外部システムは数値で指定する必要があります。'];
      return Promise.resolve([]);
    }

    const request = {
      external_system_no: externalSystemNo,
      target_object: search_condition.value.object,
    }
    // Request to server
    return Methods.apiExecute(
      'get-item-mapping-list',
      request
    ).then(response => {
      if (response.status === 200) {
        const resourceList = response.data.data.map(item => ({
        ...item,
        field_localized_no: String(item.field_localized_no)
      }));
        total_count.value = response.data ? response.data.total_count || 0 : 0;
        current_count.value = response.data
          ? response.data.current_count || 0
          : 0;
        return getFieldList().then(() => {
          loading.value = false;
          return resourceList;
        });
      }
      return [];
    });
  };

  const pageChange = val => {
    store.set(['activePage', val]);
    router.push({query: {page: val}}).catch(() => {});
  };
  const paginationChange = val => {
    itemsPerPage.value = val;
    store.set(['itemsPerPage', val]);
  };
  const sorterChange = val => {
    itemsSorter.value = val;
    store.set(['itemsSorter', val]);
    pageChange(1);
  };
  const addNewItem = () => {
    resourceList.value = [
      ...resourceList.value,
      {
        external_field_mapping_no: null,
        field_localized_no: fieldList.value.length > 0 ? String(fieldList.value[0].value) : null,
        auction_field_name: '',
        external_system_item_key: '',
        editable_flag: 1,
        _tempId: Date.now(), // 新規アイテム用の一時ID
      },
    ];
    updateTablePage();
  };
  const deleteItem = item => {
    resourceList.value = resourceList.value.filter(field => {
      // 新規追加アイテムの場合
      if (item._tempId !== undefined) {
        return item._tempId !== field._tempId;
      }
      // 既存アイテムの場合
      return item.external_field_mapping_no !== field.external_field_mapping_no;
    });
    if (item.external_field_mapping_no && item.external_field_mapping_no !== null) {
      deleteResourceList.value.push(item);
    }
    updateTablePage();
  };

  const update = () => {
    loading.value = true;
    isErrorDialog.value = false;
    validateResult.value = [];

    const processedResourceList = resourceList.value.map(item => {
      const processedItem = { ...item };

      if (processedItem.field_localized_no) {
        const selectedField = fieldList.value.find(
          field => field.value === processedItem.field_localized_no
        );
        if (selectedField) {
          processedItem.auction_field_name = selectedField.label;
        }
      }

      return processedItem;
    });

    const params = {
      external_system_no: Number(search_condition.value.extSystem),
      target_object: search_condition.value.object,
      external_field_mappings: processedResourceList,
      delete_external_field_mappings: deleteResourceList.value,
    };
    return Methods.apiExecute('regist-item-mapping-list', params).then(response => {
      if (response.status === 200) {
        getResourceList().then((data) => {
          resourceList.value = data;
          loading.value = false;
          deleteResourceList.value = [];
          confirmModal.value = false;
          compModal.value = true;
          updateTablePage();
          pageChange(1);
        })
        .catch(error => {
          loading.value = false;
          deleteResourceList.value = [];
          confirmModal.value = false;
          compModal.value = false;
          isErrorDialog.value = true;
          validateResult.value = Methods.parseHtmlResponseError(router, error);
        });
      }
    })
    .catch(error => {
      loading.value = false;
      confirmModal.value = false;
      isErrorDialog.value = true;
      validateResult.value = Methods.parseHtmlResponseError(router, error);
    });
  };

  const getLanguageName = (language_code) => {
    const language = languageList.value.find(
      lang => lang.value === language_code
    );
    return language ? language.label : '';
  };

  const updateTablePage = () => {
    pages.value =
      parseInt(resourceList.value.length / itemsPerPage.value, 10) +
      (resourceList.value.length % itemsPerPage.value > 0 ? 1 : 0);
    if (pages.value > store.activePage) {
      activePage.value = Number(pages.value);
      pageChange(pages.value); // ページ遷移を実行
    } else {
      activePage.value = store.activePage;
    }
  }
</script>
