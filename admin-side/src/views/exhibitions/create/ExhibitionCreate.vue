<template>
  <div class="mb-3">
    <CCard>
      <CCardHeader>
        <strong>商品情報</strong>
      </CCardHeader>
      <CCardBody>
        <div v-if="loading" class="my-3 text-center">
          <CSpinner />
        </div>
        <CForm onsubmit="return false;" v-show="!loading">
          <CRow class="font-xs mb-3">
            <CCol v-if="true" sm="4">
              <ImageUpload
                :origImages="stockData.picturePath"
                @onChange="onImageChange"
                @onUploadStart="onUploadStart"
                @onUploadFinish="onUploadFinish"
              />
            </CCol>
            <CCol sm="8">
              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>商品ID</label>
                  <CBadge color="danger" class="ms-auto">必須</CBadge>
                </CCol>
                <CCol sm="4">
                  <CFormInput
                    name="manage_no"
                    v-model="stockData.manage_no"
                    :disabled="itemNo ? true : false"
                    :invalid="!!errors.manage_no"
                    :feedback-invalid="errors.manage_no"
                  />
                </CCol>
              </CRow>

              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>最低入札価格</label>
                  <CBadge color="danger" class="ms-auto">必須</CBadge>
                </CCol>
                <CCol sm="3">
                  <CFormInput
                    class="text-end"
                    maxLength="8"
                    name="lowest_bid_price"
                    :invalid="!!errors.lowest_bid_price"
                    :feedback-invalid="errors.lowest_bid_price"
                    v-on:keypress="isNumber"
                    v-on:focusin="
                      () => {
                        lowest_bid_price = lowest_bid_price
                          ? Base.localeString2Number(lowest_bid_price)
                          : ''
                      }
                    "
                    v-on:focusout="
                      event => {
                        lowest_bid_price = priceLocaleString(event)
                      }
                    "
                    v-model="lowest_bid_price"
                  />
                </CCol>
                <div class="col-auto">
                  <div class="flex-row align-center">{{ currencyId }}</div>
                </div>
              </CRow>
              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>最低落札価格</label>
                  <CBadge color="danger" class="ms-auto">必須</CBadge>
                </CCol>
                <CCol sm="3">
                  <CFormInput
                    class="text-end"
                    maxLength="8"
                    name="lowest_bid_accept_price"
                    :append="currencyId"
                    :invalid="!!errors.lowest_bid_accept_price"
                    :feedback-invalid="errors.lowest_bid_accept_price"
                    v-on:keypress="isNumber"
                    v-on:focusin="
                      () => {
                        lowest_bid_accept_price = lowest_bid_accept_price
                          ? Base.localeString2Number(lowest_bid_accept_price)
                          : ''
                      }
                    "
                    v-on:focusout="
                      event => {
                        lowest_bid_accept_price = priceLocaleString(event)
                      }
                    "
                    v-model="lowest_bid_accept_price"
                  />
                </CCol>
                <div class="col-auto">
                  <div class="flex-row align-center">{{ currencyId }}</div>
                </div>
              </CRow>
              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>即決価格</label>
                </CCol>
                <CCol sm="3">
                  <CFormInput
                    class="text-end"
                    maxLength="8"
                    name="lowest_bid_accept_price"
                    :append="currencyId"
                    :invalid="!!errors.lowest_bid_accept_price"
                    :feedback-invalid="errors.lowest_bid_accept_price"
                    v-on:keypress="isNumber"
                    v-on:focusin="
                      () => {
                        lowest_bid_accept_price = lowest_bid_accept_price
                          ? Base.localeString2Number(lowest_bid_accept_price)
                          : ''
                      }
                    "
                    v-on:focusout="
                      event => {
                        lowest_bid_accept_price = priceLocaleString(event)
                      }
                    "
                    v-model="lowest_bid_accept_price"
                  />
                </CCol>
                <div class="col-auto">
                  <div class="flex-row align-center">{{ currencyId }}</div>
                </div>
              </CRow>
              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>おすすめ</label>
                </CCol>
                <CCol sm="3">
                  <CFormSwitch
                    class="text-end"
                    name="recommend_flag"
                    v-model="recommend_flag"
                  />
                </CCol>
              </CRow>
            </CCol>
          </CRow>

          <CCard
            v-for="localized in stockData.localized_json_array"
            :key="localized.language_code"
            class="mb-3"
          >
            <CCardHeader>
              <strong>{{
                constantList?.find(
                  x =>
                    x.key_string === 'LANGUAGE_CODE' &&
                    x.value1 === localized.language_code
                )?.value2
              }}</strong>
            </CCardHeader>
            <CCardBody>
              <template
                v-for="field in (localized.field_map || []).sort(
                  (a, b) => a.order - b.order
                )"
                :key="field.physical_name"
              >
                <ItemFieldInput
                  v-model="field.value"
                  :inputType="field.input_type"
                  :logicalName="field.logical_name"
                  :physicalName="field.physical_name"
                  :requiredFlag="field.required_flag"
                  :options="
                    constantList
                      .filter(
                        x =>
                          x.key_string === field.input_data_list?.key_string &&
                          x.language_code === localized.language_code
                      )
                      .map(x => {
                        return {
                          value: x.value1,
                          label: x.value2,
                        }
                      })
                  "
                  :errorMessage="
                    errors[`${field.physical_name}_${localized.language_code}`]
                  "
                  @onChange="
                    val => {
                      errors[
                        `${field.physical_name}_${localized.language_code}`
                      ] = null
                    }
                  "
                />
              </template>
            </CCardBody>
          </CCard>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CButton color="secondary" @click="goBack">一覧に戻る</CButton>
        <CButton
          :disabled="!btnUpdateEnabled || isReadOnly"
          class="mx-1"
          color="primary"
          @click="openRegistModal"
          >更新する</CButton
        >
      </CCardFooter>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="closeRegistModal"
    >
      <CModalHeader>
        <CModalTitle>{{ registModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!updateDataLoading && validateResult?.length === 0">
          この内容で{{ itemNo ? '更新' : '登録' }}してもよろしいですか？
        </div>
        <div v-if="updateDataLoading" class="my-3 text-center">
          <CSpinner />
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="registModal = false"
          color="dark"
          :disabled="updateDataLoading"
          v-if="validateResult?.length === 0"
          >キャンセル</CButton
        >
        <CButton
          @click="btnUpdate"
          color="primary"
          :disabled="updateDataLoading"
          v-if="validateResult?.length === 0"
          >OK
        </CButton>
        <CButton
          @click="closeRegistModal"
          color="primary"
          :disabled="updateDataLoading"
          v-if="validateResult?.length > 0"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>編集中止確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="cancelModal = false" color="dark">キャンセル</CButton>
        <CButton
          @click="
            () => {
              cancelModal = false
              nextPage()
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods'
import Base from '@/common/base'
import {useAuthStore} from '@/store/auth'
import {CCardBody} from '@coreui/vue'
import cloneDeep from 'lodash-es/cloneDeep'
import {onMounted, ref} from 'vue'
import {onBeforeRouteLeave, useRoute, useRouter} from 'vue-router'
import {useLotStore} from '../../../store/lot.js'
import ImageUpload from './ImageUpload.vue'
import ItemFieldInput from './ItemFieldInput.vue'

  const route = useRoute()
  const router = useRouter()
  const {setSelectedExhibition} = useLotStore()
  const {isReadOnly} = useAuthStore()

  const itemNo = route.params.itemNo

  // Constants
  const constantKeys = ref([])
  const constantList = ref([])

  const images = ref([])
  const stockData = ref({
    picturePath: [],
  })
  const currencyId = ref(null)
  const quantity = ref(null)
  const lowest_bid_price = ref('')
  const lowest_bid_accept_price = ref('')
  const lowest_bid_quantity = ref(1)
  const lowest_bid_accept_quantity = ref(1)
  const recommend_flag = ref(false)
  const loading = ref(false)
  const updateDataLoading = ref(false)
  const registModal = ref(false)
  const registModalTitle = ref('確認')
  const validateResult = ref([])
  const btnUpdateEnabled = ref(true)
  // Changing check
  const stockDataOrig = ref(null)
  const nextPage = ref(null)
  const cancelModal = ref(false)
  const btnClicked = ref(false)
  // Error handling
  const errors = ref({})

  onBeforeRouteLeave((to, from, next) => {
    console.log('beforeRouteLeave')
    if (Base.objectsAreIdentical(stockData.value, stockDataOrig.value)) {
      next()
    } else {
      nextPage.value = next
      cancelModal.value = true
    }
  })

  const getConstants = () => {
    constantList.value = []
    return Methods.apiExecute('get-constants-by-keys-language', {
      key_strings: [...constantKeys.value, 'LANGUAGE_CODE'],
    }).then(response => {
      constantList.value = response.data || []
      return Promise.resolve()
    })
  }

  const getStockData = () => {
    // Request to server
    const request = {
      item_no: Number(itemNo),
    }

    loading.value = true

    return Methods.apiExecute('get-item-detail', request).then(response => {
      if (response.status === 200 && response.data.length === 1) {
        const tmpData = response.data[0]
        // Constant keys
        constantKeys.value = tmpData.constant_key_strings || []
        // 初期化
        images.value = []

        // Prepare localized fields
        const localizedJson = (tmpData.localized_json_array || []).map(lang => {
          const fieldMap = (lang.field_map || []).map(field => {
            if (field.input_type === 'file') {
              // Convert file value to array
              if (field.value && typeof field.value === 'string') {
                field.value = [
                  {
                    id: 'local0',
                    fileName: Base.getFileName(field.value),
                    file: null,
                    s3url: field.value,
                  },
                ]
              } else if (Array.isArray(field.value)) {
                // Convert file paths to objects
                field.value = field.value.map((file, i) => ({
                  id: `local${i}`,
                  fileName: Base.getFileName(file),
                  file: null,
                  s3url: file,
                }))
              } else {
                // If field.value is an object, convert it to an array
                console.warn('Unexpected file value format:', field.value)
                field.value = []
              }
            }
            return {
              ...field,
            }
          })
          return {
            ...lang,
            field_map: fieldMap,
          }
        })

        stockData.value = {
          ...stockData.value,
          exhibition_no: tmpData.exhibition_no,
          lot_no: tmpData.lot_no,
          manage_no: tmpData.manage_no,
          localized_json_array: localizedJson,
        }
        currencyId.value = tmpData.currency_id
        quantity.value = tmpData.quantity
        lowest_bid_price.value = tmpData.lowest_bid_price
        lowest_bid_accept_price.value = tmpData.lowest_bid_accept_price
        lowest_bid_quantity.value = tmpData.lowest_bid_quantity
        lowest_bid_accept_quantity.value = tmpData.lowest_bid_accept_quantity
        recommend_flag.value = tmpData.recommend_flag === 1 ? true : false

        // Prepare pictures and videos
        stockData.value.picturePath = []
        let picturePath = []
        if (tmpData.ancillary_json_array) {
          const filter = tmpData.ancillary_json_array.filter(
            x => x.language_code === 'common' || x.language_code === 'ja'
          )
          filter.sort(ancillaryCompare)
          if (filter.length < 0) {
            return Promise.resolve(tmpData)
          }
          for (const idx in filter) {
            if (filter[idx].file_path.indexOf('.mp4') === -1) {
              // 画像
              picturePath.push({
                type: 'image',
                name: Base.getFileName(filter[idx].file_path),
                path:
                  import.meta.env.VITE_API_ENDPOINT.replace('api/', '') +
                  filter[idx].file_path,
                postar: '',
              })
            } else {
              // 動画
              console.log(filter[idx].file_path)
              picturePath.push({
                type: 'video',
                name: Base.getFileName(filter[idx].file_path),
                path:
                  import.meta.env.VITE_API_ENDPOINT.replace('api/', '') +
                  filter[idx].file_path,
                postar:
                  import.meta.env.VITE_API_ENDPOINT.replace('api/', '') +
                  filter[idx].postar_file_path,
              })
            }
          }

          // Video to bottom
          if (picturePath && picturePath.length > 0) {
            picturePath = picturePath.sort((a, b) => {
              // If video then move to bottom
              if (a.type === 'video' && b.type !== 'video') {
                return 1
              }
              if (b.type === 'video' && a.type !== 'video') {
                return -1
              }
              // Else sort by name
              return a.name > b.name ? 1 : -1
            })
          }
          console.log('Sorted images: ', picturePath)
        }
        stockData.value.picturePath = picturePath

        images.value = picturePath.map(x => {
          return {
            ...x,
            path: x.path.replace(
              process.env.VITE_API_ENDPOINT.replace('api/', ''),
              ''
            ),
            postar: x.postar.replace(
              process.env.VITE_API_ENDPOINT.replace('api/', ''),
              ''
            ),
          }
        })

        return Promise.resolve()
      }
      return Promise.reject()
    })
  }

  const goBack = () => {
    if (btnClicked.value) {
      return
    }
    btnClicked.value = true
    setSelectedExhibition(stockData.value.exhibition_no)
    router.push({path: '/exhibitions'}).catch(e => console.log(e))
  }

  const ancillaryCompare = (x, y) => {
    if (x.f3 < y.f3) {
      return -1
    }
    if (x.f3 > y.f3) {
      return 1
    }
    return 0
  }

  const openRegistModal = () => {
    registModalTitle.value = '確認'
    registModal.value = true
    updateDataLoading.value = false
    validateResult.value = []
  }

  const closeRegistModal = () => {
    registModal.value = false
    updateDataLoading.value = false
  }

  const btnUpdate = () => {
    console.log('btnUpdate')

    updateDataLoading.value = true
    validateResult.value = []

    /*
     * Sort images
     * Video to bottom
     */
    let tmpImages = images.value
    if (images.value && images.value.length > 0) {
      tmpImages = tmpImages.sort((a, b) => {
        // If video then move to bottom
        if (a.type === 'video' && b.type !== 'video') {
          return 1
        }
        if (b.type === 'video' && a.type !== 'video') {
          return -1
        }
        // Else sort by name
        return a.name > b.name ? 1 : -1
      })
    }

    // Get fields value
    const fieldValues = stockData.value.localized_json_array.map(localized => {
      const acc = (localized.field_map || []).reduce((acc, field) => {
        acc[field.physical_name] =
          typeof field.value === 'undefined' ? null : field.value
        // Get file paths for file inputs
        if (field.input_type === 'file' && field.value) {
          if (field.value && field.value.length > 1) {
            acc[field.physical_name] = field.value.map(file => file.s3url)
          } else if (field.value && field.value.length === 1) {
            acc[field.physical_name] = field.value[0].s3url || ''
          } else {
            acc[field.physical_name] = null
          }
        }
        return acc
      }, {})
      return {
        language_code: localized.language_code,
        field_map: acc,
      }
    })

    const reqParam = {
      validate: false,
      data: {
        exhibition_no: stockData.value.exhibition_no,
        lot_no: stockData.value.lot_no,
        manage_no: stockData.value.manage_no,
        item_no: itemNo,
        quantity: Base.localeString2Number(quantity.value || 0),
        lowest_bid_price: Base.localeString2Number(lowest_bid_price.value || 0),
        lowest_bid_accept_price: Base.localeString2Number(
          lowest_bid_accept_price.value || 0
        ),
        lowest_bid_quantity: Base.localeString2Number(
          lowest_bid_quantity.value || 0
        ),
        lowest_bid_accept_quantity: Base.localeString2Number(
          lowest_bid_accept_quantity.value || 0
        ),
        recommend_flag: recommend_flag.value ? 1 : 0,
        localized_json_array: fieldValues,
        picturePath: tmpImages,
      },
    }

    console.log('reqParam: ', reqParam)

    // Validate and update data
    Methods.apiExecute('upsert-item', reqParam)
      .then(response => {
        console.log('response.data: ', response.data)
        registModal.value = false
        updateDataLoading.value = false
        stockDataOrig.value = cloneDeep(stockData.value)

        // Go to list
        setSelectedExhibition(stockData.value.exhibition_no)
        router.push({path: '/exhibitions'})
      })
      .catch(error => {
        console.log(error)
        updateDataLoading.value = false
        validateResult.value = Methods.parseHtmlResponseError(router, error)
        errors.value = error?.response?.data?.errors || {}
      })
  }

  const onImageChange = files => {
    images.value = files.map(x => {
      if (x.path) {
        x.path = x.path.replace(
          import.meta.env.VITE_API_ENDPOINT.replace('api/', ''),
          ''
        )
      }
      return x
    })
  }

  const onUploadStart = () => {
    console.log('onUploadStart')
    btnUpdateEnabled.value = false
  }

  const onUploadFinish = () => {
    console.log('onUploadFinish')
    btnUpdateEnabled.value = true
  }

  const isNumber = evt => {
    const e = evt ? evt : window.event
    const charCode = e.which ? e.which : e.keyCode
    if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
      return e.preventDefault()
    }
    return true
  }

  const priceLocaleString = event => {
    return Base.priceLocaleString(event.target.value)
  }

  const getInitialStockData = () => {
    console.log('getInitialStockData')
    // Get item fields configuration
    return Promise.all([
      Methods.apiExecute('get-item-fields', {}),
      Methods.apiExecute('get-exhibition-summary', {
        exhibitionNo: [Number(route.params.exhibitionNo)],
      }),
    ]).then(results => {
      console.log('results: ', results)
      if (!results || !results.length) {
        console.error('No data found')
        return Promise.reject('No data found')
      }
      const [itemFields, exhSummary] = results.map(result => result.data)
      console.log('itemFields: ', itemFields)
      console.log('exhSummary: ', exhSummary)
      // Constant keys
      constantKeys.value =
        itemFields && itemFields.length > 0
          ? itemFields[0].constant_key_strings
          : []
      // Prepare localized fields
      const localizedJson = (itemFields || []).map(lang => {
        const fieldMap = (lang.field_list || []).map(field => {
          if (field.input_type === 'file') {
            // Convert file value to array
            if (field.value && typeof field.value === 'string') {
              field.value = [
                {
                  id: 'local0',
                  fileName: Base.getFileName(field.value),
                  file: null,
                  s3url: field.value,
                },
              ]
            } else if (Array.isArray(field.value)) {
              // Convert file paths to objects
              field.value = field.value.map((file, i) => ({
                id: `local${i}`,
                fileName: Base.getFileName(file),
                file: null,
                s3url: file,
              }))
            } else {
              // If field.value is an object, convert it to an array
              console.warn('Unexpected file value format:', field.value)
              field.value = []
            }
          }
          return {
            ...field,
          }
        })
        return {
          language_code: lang.language_code,
          field_map: fieldMap,
        }
      })

      // Initialize stockData with default values
      stockData.value = {
        ...stockData.value,
        exhibition_no: Number(route.params.exhibitionNo),
        lot_no: null,
        manage_no: null,
        localized_json_array: localizedJson,
        picturePath: [],
      }

      currencyId.value =
        exhSummary && exhSummary.length > 0 ? exhSummary[0].currency_id : ''
      lowest_bid_price.value = ''
      lowest_bid_accept_price.value = ''
      quantity.value = 1
      lowest_bid_quantity.value = 1
      lowest_bid_accept_quantity.value = 1

      return Promise.resolve()
    })
  }

  onMounted(async () => {
    try {
      loading.value = true
      if (itemNo) {
        console.log('商品情報')
        await getStockData()
        // 変更確認要
        stockDataOrig.value = cloneDeep(stockData.value)
      } else {
        console.log('商品新規追加')
        await getInitialStockData()
        stockDataOrig.value = cloneDeep(stockData.value)
      }
      // Get constants
      await getConstants()
    } catch (error) {
      console.error('Error during onMounted:', error)
      Methods.parseHtmlResponseError(router, error)
    } finally {
      loading.value = false
    }
  })
</script>

<style lang="scss" scoped>
  .swiper {
    .swiper-slide {
      background-size: cover;
      background-position: center;
    }

    &.gallery-top {
      height: 80%;
      width: 100%;
    }

    &.gallery-thumbs {
      height: 20%;
      box-sizing: border-box;
      padding: 2px 0;
    }

    &.gallery-thumbs .swiper-slide {
      width: 20%;
      height: 100%;
      opacity: 0.4;
    }

    &.gallery-thumbs .swiper-slide-active {
      opacity: 1;
    }
  }
</style>
