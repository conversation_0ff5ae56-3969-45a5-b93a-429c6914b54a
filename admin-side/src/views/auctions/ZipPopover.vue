<template>
  <div>
    <NPopover trigger="click" placement="top">
      <template #trigger>
        <CIcon
          name="cil-info"
          size="lg"
          class="cursor-pointer"
          :aria-label="ariaLabel"
        />
      </template>
      <div class="d-flex flex-column">
        <h5 class="mb-3">アップロード用のzipファイル作成手順</h5>
        <ol>
          <li>
            ルートフォルダ（例：<strong>RootFolder</strong>）を作成します。
          </li>
          <li>
            ルートフォルダ内に、<strong>商品ID</strong>（商品のID）を名前にしたサブフォルダを作成します。
          </li>
          <li>
            各<strong>商品ID</strong>フォルダ内に、<strong>.jpg</strong>または<strong>.png</strong>形式の画像ファイルを追加します。
          </li>
          <li>
            ルートフォルダを選択し、右クリックして「<strong>zip形式で圧縮</strong>」を選択します（またはWinRAR、7-Zipなどのソフトを使用）。
          </li>
          <li>
            zipファイルに名前を付け（例：<strong>UploadedFile.zip</strong>）、システムにアップロードします。
          </li>
        </ol>

        <h6 class="mt-3">フォルダ構造の例:</h6>
        <div>
          <CImage rounded :src="zipImg" width="250" height="300" />
        </div>
      </div>
    </NPopover>
  </div>
</template>

<script setup>
  import zipImg from '@/assets/images/image_zip.png'
  import {NPopover} from 'naive-ui'
  import {ref} from 'vue'

  const ariaLabel = ref('zipファイル作成ガイドを表示')
</script>
<style scoped lang="scss">
  .cursor-pointer {
    cursor: pointer;
  }
</style>
