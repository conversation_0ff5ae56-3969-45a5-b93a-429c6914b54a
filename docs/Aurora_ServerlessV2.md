# RDS Proxy 構成ガイド

## プロキシエンドポイント構成

| プロキシ                    | 環境変数           | 用途         | 対象操作                       |
| --------------------------- | ------------------ | ------------ | ------------------------------ |
| **メインプロキシ** ✍️       | `PGHOST`           | 読み書き用   | SELECT, INSERT, UPDATE, DELETE |
| **読み取り専用プロキシ** 📖 | `READ_ONLY_PGHOST` | 読み取り専用 | SELECT のみ                    |

## 使い分けルール

### 読み取り専用プロキシ 📖

```javascript
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
```

- GET操作（一覧取得、詳細表示）
- 検索・フィルタ操作、レポート生成
- 例：`get-email-notification-list`, `get-lots`

### メインプロキシ ✍️

```javascript
const pool = new PgPool(); // デフォルトでPGHOST使用
```

- INSERT/UPDATE/DELETE操作
- トランザクション処理、複合操作
- 例：ユーザー登録、データ更新、バッチ処理

## トラフィック経路図

```mermaid
graph TD
    A[Lambda Function] --> B{どちらのプロキシ？}
    B -->|PGHOST| C[メインプロキシ<br/>読み書き用]
    B -->|READ_ONLY_PGHOST| D[読み取り専用プロキシ<br/>読み取りのみ]

    C --> E[Aurora Writer Instance]
    D --> F[Aurora Reader Instance<br/>またはWriter]

    E --> G[(PostgreSQL Database)]
    F --> G

    style C fill:#ff9999
    style D fill:#99ccff
    style E fill:#ffcc99
    style F fill:#ccffcc
```

## Aurora Serverless v2 との組み合わせ

- ✅ **コネクションプーリング**: Serverlessのコールドスタート対策
- ✅ **自動スケーリング**: 負荷に応じた容量調整（0.5〜32.0 ACU）
- ✅ **高可用性**: 自動フェイルオーバーと負荷分散
- ✅ **コスト効率**: 使用量に応じた従量課金
