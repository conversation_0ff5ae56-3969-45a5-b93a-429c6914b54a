# PR文生成（ベースブランチを明示的に指定）

## 🚨 必須条件

- ベースブランチは常に **`origin/feature/demo2`** として差分を取得してください。
- 絶対に `develop` や `main` をベースにしないでください。

## 差分取得方法（明示）

以下のように、`origin/feature/demo2` をベースとした差分を取得してください：

```bash
git fetch origin feature/demo2 && \
git diff origin/feature/demo2...HEAD
```

## 3. 関連のあるIssueを確認

変更内容に関連するGitHubのIssueがあるかどうかを探します。
関連するIssueが見つかった場合に、次のステップでIssue番号を記述します。

GitHub MCPに以下の内容をリクエストしてください。

```json
{
  "owner": "GMO-MAKESHOP",
  "repo": "saas-mock",
  "per_page": 30,
  "state": "open",
  "sort": "updated",
  "direction": "desc"
}
```

## 4. PR文章を生成

PR文章の生成は次のルールを守ってください。

- ユーザーがコピペできるようMarkdownコードブロックで囲み出力する
- 動作確認内容は今回の変更からいくつかのものを書いてください。
- PR文章は以下の形式に従う（コメントはそのまま）

````markdown
```markdown
# 関連Issue

- #xxx

## 📝 このプルリクエストについて

- 概要:

## 🔧 変更内容

- [x] xxx を追加
- [x] xxx を修正
- [x] xxx を削除

## ✅ チェックリスト

- [x] ビルドが正常に通ることを確認した
- [x] 動作確認内容：
  - [ ] １．っっｘ：
  - [ ] １．っっｘ

## 📷 動作確認またはスクリーンショット

## 💬 補足事項

- 例: XXX
```
````
