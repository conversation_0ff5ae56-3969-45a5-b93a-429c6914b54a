I'm getting errors when calling backend APIs from pages that should be accessible without login. Here's the current situation and what I need help with:

**Current Problem:**

- When accessing the Top page without login, two APIs are automatically triggered during component mount:
  1. `auction_search-auction-items` infrastructure/common/auction-side/gateway-resource/search-auction-items/source/index.js
  2. `get-item-search-constants` infrastructure/common/auction-side/gateway-resource/get-item-search-constants/source/index.js
- These API calls are failing because they require authentication, but the Top page should be accessible to unauthenticated users

**Current Architecture:**

- Multi-tenant SPA for Auction system using Vue 3 + AWS (CloudFront, S3, RDS PostgreSQL)
- Authentication via Cognito + Amplify with user info stored in `admin-side/src/store/auth.js`
- API Gateway uses Cognito authorizer, requiring `idToken` in `Authorization` header
- Some pages should be public (Top page with BidConfirmModal.vue, Detail page) while others require login (My page, Place bid functionality)
- Router configuration in `auction-side/src/router/index.js` correctly sets Top page as not requiring authentication

**What I Need:**

1. xxx
2. yyy

**Key Requirements:**

- Maintain security for authenticated-only endpoints
- Allow public access to auction item search and search constants
- Preserve existing authentication flow for protected pages
- Follow the project's existing patterns and architecture

Please start with step 1: analyzing the codebase and creating a comprehensive plan, save plan to markdown file to me review
