# Display Area (表示エリア) Feature Implementation

## Overview
This document describes the implementation of the new "表示エリア" (Display Area) feature for the admin interface. This feature adds a category field that allows users to specify display areas for field configurations.

## Requirements Implemented

### 1. Field Specifications
- **Field Name**: 表示エリア (Display Area)
- **Field Type**: Dropdown selection
- **Options**: 
  - 商品詳細（タグ）
  - 商品詳細（表）
- **Validation**: Required only when page selection equals "item_detail"

### 2. User Interface Integration
- Added to DisplayItem.vue component as a conditional dropdown
- Appears only when "商品詳細" (item_detail) page is selected
- Follows existing CoreUI component patterns
- Includes client-side validation

## Implementation Details

### Database Changes

#### 1. Table Schema Update
**File**: `dll/table/41.m_field_mapping_add_display_area.sql`
```sql
ALTER TABLE public.m_field_mapping 
ADD COLUMN display_area character varying;
```

#### 2. Function Update
**File**: `dll/function/admin-side/f_regist_update_display_field_list.sql`
- Added `in_display_area character varying` parameter
- Updated INSERT and UPDATE statements to handle display_area column
- Maintains backward compatibility

### Backend API Changes

#### 1. Validation Rules
**File**: `infrastructure/common/admin-side/gateway-resource/regist-display-field-list/source/validation-rules.js`
- Added conditional validation for display_area field
- Required only when window_id equals "item_detail"

#### 2. API Handler
**File**: `infrastructure/common/admin-side/gateway-resource/regist-display-field-list/source/index.js`
- Added display_area parameter to SQL parameters array
- Maintains existing error handling patterns

### Frontend Changes

#### 1. Main Component
**File**: `admin-side/src/views/tenant/DisplayItem.vue`

**Template Changes**:
```vue
<CRow v-if="search_condition.window_id === 'item_detail'" class="mt-3">
  <CCol sm="2"> 表示エリア </CCol>
  <CCol sm="5">
    <CFormSelect
      name="display_area"
      :options="displayAreaOptions"
      v-model="search_condition.display_area"
    />
  </CCol>
</CRow>
```

**Script Changes**:
- Added displayAreaOptions with hardcoded values
- Updated search_condition reactive object to include display_area
- Added client-side validation in search() and saveDisplayItems() functions
- Updated API call parameters to include display_area

#### 2. Data Structure
```javascript
const displayAreaOptions = ref([
  {value: '商品詳細（タグ）', label: '商品詳細（タグ）'},
  {value: '商品詳細（表）', label: '商品詳細（表）'},
]);

const search_condition = ref({
  window_id: 'top',
  language_code: '',
  display_area: '',
});
```

## Validation Logic

### Backend Validation
- Conditional required check: display_area is required only when window_id === 'item_detail'
- Uses existing validation framework patterns

### Frontend Validation
- Client-side validation prevents API calls with invalid data
- Error messages displayed in existing modal dialog
- Validation triggers on both search and save operations

## Usage Instructions

### For Users
1. Navigate to 商品情報表示設定管理 (Display Item Settings)
2. Select "商品詳細" from the 画面選択 (Page Selection) dropdown
3. The "表示エリア" (Display Area) field will appear
4. Select either "商品詳細（タグ）" or "商品詳細（表）"
5. Configure field mappings as usual
6. Save changes

### For Developers
1. Apply database migration: `41.m_field_mapping_add_display_area.sql`
2. Deploy updated backend API functions
3. Deploy updated Vue.js components
4. Test functionality across different browsers

## Testing

### Manual Testing Checklist
- [ ] Display Area dropdown appears when item_detail page is selected
- [ ] Display Area dropdown is hidden for other page selections
- [ ] Validation prevents save when display_area is empty for item_detail
- [ ] Data is correctly saved to m_field_mapping.display_area column
- [ ] Existing functionality remains unchanged for other pages

### Automated Testing
- Test script available: `test_display_area_implementation.sh`
- Validates all file modifications are in place
- Checks for required code patterns

## Migration Notes

### Database Migration
```sql
-- Run this migration to add the display_area column
\i dll/table/41.m_field_mapping_add_display_area.sql
```

### Rollback Plan
If rollback is needed:
```sql
ALTER TABLE public.m_field_mapping DROP COLUMN display_area;
```

## Troubleshooting

### Common Issues
1. **Display Area field not appearing**: Check if window_id === 'item_detail'
2. **Validation errors**: Ensure display_area is selected for item_detail page
3. **Database errors**: Verify migration was applied correctly
4. **API errors**: Check backend logs for validation rule failures

### Debug Information
- Backend API: regist-display-field-list endpoint
- Database function: f_regist_update_display_field_list
- Frontend component: DisplayItem.vue
- Validation: Conditional on constPageOptions value 'item_detail'

## Future Enhancements

### Potential Improvements
1. Dynamic dropdown options from database constants
2. Multi-select display areas
3. Display area-specific field filtering
4. Audit logging for display area changes

### Extensibility
The implementation follows existing patterns and can be easily extended:
- Additional dropdown options can be added to displayAreaOptions
- Validation rules can be modified in validation-rules.js
- Database schema supports longer display_area values if needed
