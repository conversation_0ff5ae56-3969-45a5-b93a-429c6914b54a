# hummer_flag (Winning Flag) Detailed Documentation

## Overview

`hummer_flag` is an important flag for managing the winning status of auction items. This flag is used in the exhibition item (`t_exhibition_item`) and exhibition result (`t_exhibition_result`) tables, playing a central role in controlling the auction bidding and winning process.

## Data Type and Possible Values

- **Data Type**: `integer`
- **Default Value**: `0`
- **Possible Values**:
  - `0`: Undetermined state (bidding available)
  - `1`: Won (SOLD OUT)
  - `2`: Unsold (bidding unsuccessful)

## Tables Used

### 1. t_exhibition_item (Exhibition Item Table)

```sql
hummer_flag integer DEFAULT 0
```

### 2. t_exhibition_result (Exhibition Result Table)

```sql
hummer_flag integer NOT NULL
```

### 3. Example Usage in t_exhibition_result

```sql
COMMENT ON COLUMN t_exhibition_result.hummer_flag IS 'Winning Flag';
```

## Detailed Business Logic

### 1. Usage in Bidding Process (f_regist_bid.sql)

#### Bidding Eligibility Check

```sql
-- Error if winning result is already determined (unsold, won)
ELSEIF exhibition_rec.hummer_flag <> 0 THEN
  error_code := 'HUMMER_ERROR';
```

#### Buy-Now Processing

```sql
-- When there's a bid exceeding the buy-now price
IF ext_deal_flag = 1 AND exhibition_rec.deal_bid_price <= price THEN
  UPDATE t_exhibition_item
    SET current_price = $1
      , bid_success_member_no = $2
      , top_member_no = $2
      , hummer_flag = 1  -- Set to winning state
      , bid_success_price = $1
  WHERE exhibition_item_no = $3;
END IF;
```

### 2. Auction End Processing (f_batch_exhibition_item_auction_end.sql)

#### Unsold Processing (hummer_flag = 2)

```sql
-- Set as unsold if minimum winning price/quantity not reached
UPDATE t_exhibition_item TEI
   SET bid_success_member_no = NULL,
       hummer_flag = 2,  -- Unsold state
       update_datetime = now()
  FROM target_items TI
 WHERE TEI.exhibition_item_no = TI.exhibition_item_no
   AND (conditions for not meeting minimum winning criteria)
```

#### Winning Processing (hummer_flag = 1)

```sql
-- Set as won if conditions are met
UPDATE t_exhibition_item TEI
    SET bid_success_member_no = (highest bidder),
        top_member_no = (highest bidder),
        current_price = (current price),
        top_price = (highest bid price),
        hummer_flag = 1,  -- Winning state
        update_datetime = now()
  FROM target_items TI
 WHERE TEI.exhibition_item_no = TI.exhibition_item_no
   AND TEI.hummer_flag = 0  -- Only undetermined items
```

### 3. Search and Filtering Processing

#### Auction Item Search (f_search_auction_items.sql)

```sql
-- Filter to exclude sold out items
AND (in_un_sold_out IS NULL OR NOT in_un_sold_out OR NOT (TEI.hummer_flag = 1))

-- Exclude determined items
AND (in_exhibition_nos IS NOT NULL OR TEI.hummer_flag = 0)

-- Determine sold out status
'sold_out', TEI.hummer_flag = 1,
```

#### Display Control in CSV Output (f_search_auction_items_for_csv.sql)

```sql
CASE WHEN TEI.hummer_flag = 1 THEN 'SOLD OUT'
     WHEN TEI.hummer_flag = 2 THEN ''  -- Empty string for unsold
     ELSE TEI.lowest_bid_price :: text
END
```

### 4. Notification and Result Processing

#### Winning Notification Processing

```sql
-- Extract notification targets for winners
'successful', TEI.top_member_no = MM.member_no AND TEI.hummer_flag = 1
```

#### Result CSV Output

```sql
'hummerFlag', CASE WHEN TER.hummer_flag = 1 THEN 'OK' ELSE 'NG' END
```

#### Admin Interface Display

```javascript
// Display control in JavaScript
hummerFlag: row.hummer_flag === 1 ? 'Won' : 'Unsold'
```

## Frontend (Vue.js) Usage Examples

### Button Control in Admin Interface (ExhibitionTable.vue)

```vue
<!-- Disable condition for listing stop button -->
:class=" exhibitionEndFlag || item.cancel_flag > 0 || item.hummer_flag !== 0 //
Disable if determined ? 'btn button btn-dark disabled' : '' "

<!-- Disable condition for listing delete button -->
:class=" exhibitionEndFlag || item.cancel_flag > 0 || item.hummer_flag !== 0 //
Disable if determined ? 'btn button btn-dark disabled' : '' "

<!-- Display condition for listing restore button -->
v-if=" item.cancel_flag === 1 && !exhibitionEndFlag && item.hummer_flag === 0 //
Show only if undetermined "
```

## State Transition Diagram

```
[Initial State]
hummer_flag = 0 (Undetermined)
    ↓
[During Bidding Process]
- Bidding available
- Immediately set hummer_flag = 1 when buy-now price achieved
    ↓
[Determination at Auction End]
    ↓
┌─────────────────────────────┐
│  Minimum Winning Condition  │
│  Check                      │
└─────────────────────────────┘
    ↓                    ↓
[Conditions Met]       [Conditions Not Met]
hummer_flag = 1        hummer_flag = 2
(Won)                  (Unsold)
    ↓                    ↓
[Final State]          [Final State]
- Bidding not allowed  - Bidding not allowed
- Winner determined    - No winner
- SOLD OUT display     - Empty string display
```

## Related Constraints and Triggers

### Database Constraints

- `t_exhibition_item.hummer_flag`: Default value `0`
- `t_exhibition_result.hummer_flag`: `NOT NULL` constraint

### Business Constraints

1. **Bidding Constraints**: When `hummer_flag <> 0`, new bids are rejected with `HUMMER_ERROR`
2. **Management Operation Constraints**: When `hummer_flag <> 0`, listing stop/delete operations are disabled
3. **Display Constraints**: State-based display control in frontend

## Usage Scenarios List

### Database Functions

- **Bidding Processing**: `f_regist_bid.sql`, `f_regist_bid_sealed.sql`
- **Search & Retrieval**: `f_search_auction_items.sql`, `f_get_successful_bid_item.sql`
- **Batch Processing**: `f_batch_exhibition_item_auction_end.sql`
- **Notification Processing**: `f_get_member_info_for_exhibition_end_notification.sql`
- **CSV Output**: `f_search_auction_items_for_csv.sql`
- **Management Functions**: `f_clear_top_bid.sql`, `f_get_lots.sql`

### Frontend

- **Admin Interface**: State control in Vue.js components
- **API Response**: Display string conversion in JavaScript

### Infrastructure

- **CSV Output**: State display in result files
- **Notification System**: Determination of winning/unsold status

## Important Notes

1. **Irreversibility**: Once `hummer_flag` is set to `1` or `2`, it cannot be returned to `0` in normal business flow
2. **Synchronization**: `hummer_flag` in `t_exhibition_item` and `t_exhibition_result` must be managed synchronously
3. **Performance**: Appropriate index design is important due to frequent use in search queries
4. **Internationalization**: Display strings ('Won', 'Unsold', 'SOLD OUT') are fixed and need consideration for multi-language support

## Related Documents

- [Auction Specification Overview](../README.md#auction-specification-overview)
- [Database Schema](../dll/table/)
- [API Specifications](../infrastructure/)
