# Primary Identifier Usage Analysis

This document analyzes the usage patterns and relationships between the four main identifier types in the auction system: `manage_no`, `item_no`, `lot_no`, and `exhibition_item_no`.

## Overview

The auction system uses a multi-layered identifier strategy to support both business operations and technical requirements. Each identifier serves a specific purpose in the data flow from item creation to auction completion.

## Identifier Comparison

| Field                    | Type                | Purpose             | Scope         | Stability   | User Visibility | Primary Use Cases                              |
| ------------------------ | ------------------- | ------------------- | ------------- | ----------- | --------------- | ---------------------------------------------- |
| **`manage_no`**          | `character varying` | Business ID         | Item-level    | Permanent   | ✅ High         | Business operations, external APIs, CSV import |
| **`item_no`**            | `bigint`            | Internal ID         | Item-level    | Permanent   | ❌ Hidden       | Database FK, system operations                 |
| **`lot_no`**             | `bigint`            | Grouping ID         | Lot-level     | Per-auction | ❌ Hidden       | Auction grouping, bidding units                |
| **`exhibition_item_no`** | `bigint`            | Auction instance ID | Listing-level | Per-listing | ❌ Hidden       | Bidding, favorites, auction operations         |

## Detailed Identifier Analysis

### 1. `manage_no` - Business Item Identifier

**Definition**: User-defined, business-meaningful identifier for items
**Data Type**: `character varying NOT NULL`

The primary business identifier that provides human-readable item identification across all business operations.

**Key Characteristics**:

- ✅ **Human Readable**: Examples: "PROD-001", "ITEM-2024-001"
- ✅ **Business Meaningful**: Maps directly to external inventory systems
- ✅ **Cross-Auction Stable**: Maintains same ID across multiple auction listings
- ✅ **External API Friendly**: Preferred for integrations and data exchange
- ⚠️ **Validation Required**: Must check for duplicates during creation

**Use Cases**:

- User-facing interfaces and reports
- CSV import/export operations
- External system integration
- Business correspondence and documentation

### 2. `item_no` - Internal Item Identifier

**Definition**: Auto-generated internal identifier for database operations
**Data Type**: `bigint PRIMARY KEY` (Auto-generated sequence)

The system's primary key for item records, optimized for database performance and internal operations.

**Key Characteristics**:

- ✅ **Always Unique**: System-guaranteed uniqueness
- ✅ **Performance Optimized**: Numeric indexing for fast queries
- ✅ **Foreign Key Friendly**: Used in all database relationships
- ❌ **No Business Meaning**: Sequential numbers (e.g., 1001, 1002)
- ❌ **Internal Use Only**: Never exposed to end users

**Use Cases**:

- Database foreign key relationships
- Internal system processing
- Performance-critical operations
- System logging and debugging

### 3. `lot_no` - Internal Lot Identifier

**Definition**: Auto-generated identifier for auction lot groupings
**Data Type**: `bigint DEFAULT nextval('t_lot_no_seq'::regclass)`

Groups multiple items together as a single auction unit with unified bidding conditions.

**Key Characteristics**:

- ✅ **Multi-Item Grouping**: One lot can contain multiple items
- ✅ **Auction Unit**: Defines minimum bidding unit
- ✅ **System Generated**: Auto-incremented sequence
- ❌ **Per-Auction Instance**: New ID assigned when re-listed
- ❌ **Internal Only**: Hidden from user interfaces

**Related Business ID**: `lot_id` (user-facing lot identifier)

**Business Rules**:

- One lot can contain multiple items
- Represents minimum unit for auction listing
- Bidding prices and conditions are set per lot

### 4. `exhibition_item_no` - Auction Instance Identifier

**Definition**: Unique identifier for each auction listing instance

Tracks individual auction participations, allowing the same lot to be listed in multiple auctions.

**Key Characteristics**:

- ✅ **Unique Per Listing**: Each auction participation gets new ID
- ✅ **Bidding Operations**: Central identifier for bid processing
- ✅ **Auction State Management**: Tracks favorites, bids, and results
- ❌ **Instance Specific**: Changes with every re-listing
- ❌ **Not Product Identity**: Represents listing instance, not the product itself

## System Architecture

### Item Creation Flow

```mermaid
sequenceDiagram
    participant Admin as Admin User
    participant Frontend as Vue Frontend
    participant API as Lambda API
    participant DB as PostgreSQL

    Admin->>Frontend: Input manage_no "PROD-001"
    Frontend->>API: POST /upsert-item {manage_no: "PROD-001"}
    API->>API: Validate manage_no (required, format)
    API->>DB: f_get_item_by_manage_no("PROD-001")
    DB-->>API: Check if exists (duplicate validation)
    API->>DB: INSERT t_item (manage_no="PROD-001")
    DB->>DB: Auto-generate item_no via sequence
    DB-->>API: Return {item_no: 1001, manage_no: "PROD-001"}
    API-->>Frontend: Success response with both IDs
    Frontend-->>Admin: Show success, display manage_no
```

### Data Relationship Structure

```mermaid
erDiagram
    t_tenant ||--o{ t_item : contains
    t_item ||--o{ t_item_localized : "has translations"
    t_item ||--o{ t_lot_detail : "grouped in"
    t_lot_detail }o--|| t_lot : "belongs to"
    t_lot ||--o{ t_exhibition_item : "listed as"
    t_exhibition_item }o--|| t_exhibition : "part of"
    t_exhibition ||--o{ t_exhibition_result : "produces"
    t_exhibition_result }o--|| t_exhibition_item : "references"

    t_item {
        bigint item_no PK
        string manage_no "Business ID"
        bigint tenant_no FK
    }

    t_item_localized {
        bigint item_no FK
        string language_code
        string localized_data
    }

    t_lot_detail {
        bigint lot_detail_no PK
        bigint lot_no FK
        bigint item_no FK
        int order_no "Display Order"
    }

    t_lot {
        bigint lot_no PK
        string lot_id "Business ID"
    }

    t_exhibition_item {
        bigint exhibition_item_no PK
        bigint exhibition_no FK
        bigint lot_no FK
    }

    t_exhibition {
        bigint exhibition_no PK
        string exhibition_name
    }

    t_exhibition_result {
        bigint exhibition_result_no PK
        bigint exhibition_no FK
        bigint exhibition_item_no FK
        bigint lot_no FK
        bigint item_no FK
        int order_no "Invoice Serial"
    }
```

### Data Flow Process

```mermaid
flowchart LR
    A[External System] --> B[manage_no]
    B --> C[item_no]
    C --> D[lot_detail]
    D --> E[lot_no]
    E --> F[exhibition_item_no]

    A1[Business ID] --> B1[Product]
    B1 --> C1[Item Record]
    C1 --> D1[Lot Link]
    D1 --> E1[Auction Lot]
    E1 --> F1[Auction Instance]

    subgraph "Identifier Types"
        B & B1
        C & C1
        D & D1
        E & E1
        F & F1
    end
```

## Usage Guidelines

### Primary Identifier Usage

**Analysis Result**: `item_no` is the dominant identifier, used in approximately 60% of system operations.

### Best Practices

#### 1. Technical Processing → Use `item_no`

- Database internal operations
- Foreign key relationships
- Performance-critical processing
- System-to-system communication

#### 2. Business Processing → Use `manage_no`

- User-facing interfaces
- External system integration
- Reports and CSV exports
- Customer communication

#### 3. Complex Operations → Combined Usage

- Accept user input via `manage_no`
- Process internally using `item_no`
- Output format depends on context and audience

## Identifier Classification

### System Internal IDs (Auto-generated)

- `item_no`: Item internal identifier
- `lot_no`: Lot internal identifier
- `exhibition_no`: Exhibition internal identifier
- `exhibition_item_no`: Exhibition item internal identifier

### Business IDs (User-defined)

- `manage_no`: Item business identification code
- `lot_id`: Lot business identification code
