/**
 * End-to-end test for language switching functionality
 */

import {expect, test} from '@playwright/test'

test.describe('Language Switching E2E', () => {
  test.beforeEach(async ({page}) => {
    // Navigate to the application
    await page.goto('/')
  })

  test('should switch language using PC selector', async ({page}) => {
    // Find the PC language selector
    const pcSelector = page.locator('#locale-switcher')
    await expect(pcSelector).toBeVisible()

    // Verify initial language is Japanese
    await expect(pcSelector).toHaveValue('ja')

    // Change to English
    await pcSelector.selectOption('en')

    // Verify language has changed
    await expect(pcSelector).toHaveValue('en')

    // Check if UI has updated (look for English text)
    // This will depend on your specific UI elements
    await expect(page.locator('text=EN')).toBeVisible()
  })

  test('should switch language using SP selector', async ({page}) => {
    // Set mobile viewport
    await page.setViewportSize({width: 375, height: 667})

    // Find the SP language selector
    const spSelector = page.locator('#locale-switcher-sp')
    await expect(spSelector).toBeVisible()

    // Verify initial language is Japanese
    await expect(spSelector).toHaveValue('ja')

    // Change to English
    await spSelector.selectOption('en')

    // Verify language has changed
    await expect(spSelector).toHaveValue('en')
  })

  test('should persist language preference across page refreshes', async ({page}) => {
    // Change language to English
    const pcSelector = page.locator('#locale-switcher')
    await pcSelector.selectOption('en')

    // Refresh the page
    await page.reload()

    // Verify language is still English
    await expect(pcSelector).toHaveValue('en')
  })

  test('should synchronize PC and SP selectors', async ({page}) => {
    // Change PC selector to English
    const pcSelector = page.locator('#locale-switcher')
    await pcSelector.selectOption('en')

    // Verify SP selector also shows English
    const spSelector = page.locator('#locale-switcher-sp')
    await expect(spSelector).toHaveValue('en')
  })

  test('should handle language switching for authenticated users', async ({page}) => {
    // This test would require setting up authentication
    // Skip for now if not authenticated
    test.skip(!process.env.TEST_WITH_AUTH, 'Requires authentication setup')

    // Perform login (implementation depends on your auth system)
    // await login(page)

    // Change language
    const pcSelector = page.locator('#locale-switcher')
    await pcSelector.selectOption('en')

    // Verify API call was made (could check network requests)
    // This would require intercepting network requests
  })

  test('should handle network errors gracefully', async ({page}) => {
    // Intercept and fail the language change API call
    await page.route('**/private/change-language', route => {
      route.abort('failed')
    })

    // Try to change language
    const pcSelector = page.locator('#locale-switcher')
    await pcSelector.selectOption('en')

    // Language should still change in UI even if API fails
    await expect(pcSelector).toHaveValue('en')
  })
})
