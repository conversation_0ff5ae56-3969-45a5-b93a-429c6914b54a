<script setup>
  import {computed, defineAsyncComponent, defineProps} from 'vue'
  import {useLocale} from 'vuetify'
  import {CLASSIFICATIONS} from '../../../defined/const'
  import {useSearchResultStore} from '../../../stores/search-results'

  const AuctionContentHistory = defineAsyncComponent(
    () => import('../parts/AuctionContentHistory.vue')
  )
  const AuctionItem = defineAsyncComponent(() => import('./AuctionItem.vue'))

  defineProps(['exhibitionInfo', 'productList'])
  const searchResultStore = useSearchResultStore()
  const isAscendingActive = computed(
    () => searchResultStore.selectedAucClassification === CLASSIFICATIONS.ASCENDING
  )

  const {t} = useLocale()
</script>
<template>
  <div class="auction-conteiner">
    <AuctionContentHistory :productList="productList">
      <template v-slot:header>
        <tr>
          <th rowspan="1" colspan="1">{{ t('bidHistory.endDate') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.productName') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.sim') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.capacity') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.color') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.rank') }}</th>
          <th rowspan="1" colspan="1">
            {{
              isAscendingActive
                ? t('bidHistory.bidSuccessPrice')
                : t('bidHistory.bidSuccessUnitPrice')
            }}
          </th>
          <th v-if="!isAscendingActive" rowspan="1" colspan="1">
            {{ t('bidHistory.bidSuccessQuantity') }}
          </th>
          <th v-if="!isAscendingActive" rowspan="1" colspan="1">
            {{ t('bidHistory.bidTotalPrice') }}
          </th>
        </tr>
      </template>
      <template v-slot:item="{item}">
        <AuctionItem :item="item" :isAscendingAuction="isAscendingActive" />
      </template>
    </AuctionContentHistory>
  </div>
</template>
