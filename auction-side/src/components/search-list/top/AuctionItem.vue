<script setup>
  import {computed, defineProps, onMounted, ref} from 'vue'
  import {useLocale} from 'vuetify'
  import {priceLocaleString} from '../../../composables/common'
  import useFavorite from '../../../composables/favorite'
  import {useAuthStore} from '../../../stores/auth'
  import {usePrevRouteStore} from '../../../stores/prev-route'
  import {getAuctionStatusLabel, isLowerThanSM, navigateToPath} from '../../../utils'

  const props = defineProps(['item', 'favorite', 'isAscendingAuction'])

  const auth = useAuthStore()
  const {toggleFavorite} = useFavorite()
  const {t} = useLocale()
  const {goToPath} = usePrevRouteStore()

  const itemRef = computed(() => props.item)

  const favoriteActiveClass = ref(false)

  onMounted(() => {
    favoriteActiveClass.value = !!props.item?.attention_info?.is_favorited
  })

  const handleFavorite = () => {
    toggleFavorite(props.item.exhibition_item_no, favoriteActiveClass.value)
    if (auth.isAuthenticated) {
      favoriteActiveClass.value = !favoriteActiveClass.value
    }
    if (props.favorite) {
      props.favorite()
    }
  }

  const statusLabel = getAuctionStatusLabel(itemRef, t)
</script>

<template>
  <tr class="auction-item-row">
    <!-- Maker -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'maker'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.maker') }}：</div>
      {{ item.free_field?.maker }}
    </td>

    <!-- Product Name -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'productName'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.productName') }}：</div>
      <a @click="navigateToPath(item.link, goToPath, $route)" class="cursor-pointer">
        {{ item.free_field?.product_name }}
      </a>
    </td>

    <!-- SIM -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'sim'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.sim') }}：</div>
      {{ item.free_field?.sim }}
    </td>

    <!-- Capacity -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'capacity'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.capacity') }}：</div>
      {{ item.free_field?.capacity }}
    </td>

    <!-- Color -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'color'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.color') }}：</div>
      {{ item.free_field?.color }}
    </td>

    <!-- Rank -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'grade'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.rank') }}：</div>
      {{ item.free_field?.rank }}
    </td>

    <!-- Quantity -->
    <td v-if="!isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'quantity'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.quantity') }}：</div>
      {{ priceLocaleString(item.bid_status?.quantity) }}
    </td>
    <!-- 開始価格 -->
    <td v-if="isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'price-min'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.startPrice') }}：</div>
      <span class="current-price">
        {{
          `${t('productDetail.currency')}${priceLocaleString(item.bid_status?.lowest_bid_price)}`
        }}
      </span>
    </td>

    <!-- Lowest Bid Quantity -->
    <td v-if="!isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'lowestBid'">
      <div v-show="isLowerThanSM">{{ t('productDetail.lowestBidQuantity') }}：</div>
      {{ priceLocaleString(item.bid_status?.lowest_bid_quantity) }}
    </td>
    <!-- 現在価格 -->
    <td v-if="isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'current-price'">
      <div v-show="isLowerThanSM" class="sp">{{ t('productDetail.info.currentPrice') }}：</div>
      <div class="item-flex-center">
        <span class="current-price sp-text-red">
          {{ `${t('productDetail.currency')}${priceLocaleString(item.bid_status?.current_price)}` }}
        </span>
      </div>
    </td>

    <!-- Lowest Bid Price -->
    <td v-if="!isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'price-min'">
      <div v-show="isLowerThanSM">{{ t('productDetail.lowestBidPrice') }}：</div>
      <div class="item-flex-center">
        <span class="unit">{{ t('productDetail.currency') }}</span>
        <span>
          {{ priceLocaleString(item.bid_status?.lowest_bid_price) }}
        </span>
      </div>
    </td>
    <!-- 入札 -->
    <td v-if="isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'time-remaining'">
      <div v-show="isLowerThanSM">{{ t('BID_STATUS') }}：</div>
      <div>
        <span class="time">
          {{ statusLabel }}
        </span>
        <span v-if="auth.isAuthenticated && item.bid_status?.is_top_member" class="highest">
          {{ t('HIGHEST_BIDDER') }}
        </span>
      </div>
    </td>

    <!-- Favorite -->
    <td :class="isLowerThanSM ? 'sp-cell-flex check' : 'check'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.favorite') }}：</div>
      <button class="fav-mark" @click="handleFavorite">
        <p :class="{'fav-pct': true, added: favoriteActiveClass}"></p>
      </button>
    </td>
  </tr>
</template>

<style lang="css" scoped>
  .sp-cell-flex {
    display: flex !important;
    justify-content: space-between;
  }
  .item-flex-center {
    display: flex;
    justify-content: center;
  }
  .sp-cell-flex .time {
    font-size: 3vw;
    font-weight: 600;
    margin: 0 1vw;
  }
  .sp-cell-flex .highest {
    display: inline-block !important;
    color: #ff0000;
    font-weight: 500;
    font-size: 3vw;
  }
  .sp-text-red {
    font-weight: 600 !important;
  }

  /* Small devices (landscape phones, less than 768px) */
  @media screen and (max-width: 767.98px) {
    .sp-text-red {
      color: #e60012;
      font-weight: 600;
    }
  }
</style>
