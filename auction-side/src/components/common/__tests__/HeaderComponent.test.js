/**
 * Simplified test for HeaderComponent language switching functionality
 */

import HeaderComponent from '@/components/common/HeaderComponent.vue'
import {mount} from '@vue/test-utils'
import {beforeEach, describe, expect, it, vi} from 'vitest'

// Mock the language store
vi.mock('@/stores/language', () => ({
  useLanguageStore: () => ({
    currentLanguage: 'ja',
  }),
  default: () => ({
    changeLanguage: vi.fn(),
  }),
}))

// Mock the auth store
vi.mock('@/stores/cognitoAuth', () => ({
  useCognitoAuthStore: () => ({
    isAuthenticated: false,
    logout: vi.fn(),
  }),
}))

// Mock router
vi.mock('vue-router', () => ({
  RouterLink: 'router-link',
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock constants
vi.mock('@/defined/const', () => ({
  PATH_NAME: {
    TOP: '/',
    FAVORITES: '/favorites',
    BIDS: '/bids',
    BID_HISTORY: '/bid-history',
    COGNITO_REGISTER: '/register',
  },
}))

describe('HeaderComponent Language Switching', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(HeaderComponent, {
      global: {
        stubs: {
          RouterLink: true,
        },
      },
    })
  })

  describe('Language Selector Rendering', () => {
    it('renders PC language selector', () => {
      const pcSelector = wrapper.find('#locale-switcher')
      expect(pcSelector.exists()).toBe(true)
    })

    it('renders SP language selector', () => {
      const spSelector = wrapper.find('#locale-switcher-sp')
      expect(spSelector.exists()).toBe(true)
    })

    it('contains both language options in PC selector', () => {
      const pcSelector = wrapper.find('#locale-switcher')
      const options = pcSelector.findAll('option')

      expect(options).toHaveLength(2)
      expect(options[0].attributes('value')).toBe('ja')
      expect(options[1].attributes('value')).toBe('en')
    })

    it('contains both language options in SP selector', () => {
      const spSelector = wrapper.find('#locale-switcher-sp')
      const options = spSelector.findAll('option')

      expect(options).toHaveLength(2)
      expect(options[0].attributes('value')).toBe('ja')
      expect(options[1].attributes('value')).toBe('en')
    })

    it('displays correct option labels', () => {
      const pcSelector = wrapper.find('#locale-switcher')
      const options = pcSelector.findAll('option')

      expect(options[0].text()).toBe('JP')
      expect(options[1].text()).toBe('EN')
    })
  })

  describe('Language Change Functionality', () => {
    it('has handleLanguageChange method', () => {
      expect(typeof wrapper.vm.handleLanguageChange).toBe('function')
    })

    it('selectors have change event handlers', () => {
      const pcSelector = wrapper.find('#locale-switcher')
      const spSelector = wrapper.find('#locale-switcher-sp')

      expect(pcSelector.attributes('onChange')).toBeDefined()
      expect(spSelector.attributes('onChange')).toBeDefined()
    })
  })
})
