import type {FormattedAuctionItem} from '@/composables/_type'

export type ProductListHandlers = {
  /** Handle favorite toggle action */
  onFavoriteToggle?: (exhibitionItemNo: string, currentFavorited: boolean) => Promise<void>
  /** Handle bid placement */
  onBid?: (item: FormattedAuctionItem, bidPrice: string, bidQuantity: string) => Promise<void>
  /** Handle refresh action */
  onRefresh?: () => Promise<void>
  /** Handle item click navigation */
  onItemClick?: (item: FormattedAuctionItem) => void
}

/** Auction status CSS class types */
// soldout: SOLD OUT
// closed: オークション終了
// extended: 延長中
// preauc：下見観覧中
// '': 「New」オークション
export type AuctionStatusClass = 'soldout' | 'closed' | 'preauc' | 'extended' | ''
