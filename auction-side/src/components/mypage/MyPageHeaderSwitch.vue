<script setup>
  import {PATH_NAME} from '@/defined/const'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'

  const route = useRoute()
  const {t: translate} = useLocale()

  const isActive = path => {
    return route.path === path ? 'active' : ''
  }
</script>
<template>
  <h2 class="page-ttl mypage">
    <p class="ttl">マイページ</p>
    <p class="sub">Mypage</p>
  </h2>

  <section id="mypage-head">
    <div class="container">
      <div class="nav-wrap">
        <div class="nav-content" :class="isActive(PATH_NAME.FAVORITES)">
          <RouterLink :to="PATH_NAME.FAVORITES">
            <span class="favorite"></span>
            <div class="label">{{ translate('HEADER_NAV_FAVORITES') }}</div>
          </RouterLink>
        </div>
        <div class="nav-content" :class="isActive(PATH_NAME.BIDS)">
          <RouterLink :to="PATH_NAME.BIDS">
            <span class="bidding"></span>
            <div class="label">{{ translate('HEADER_NAV_BIDDING') }}</div>
          </RouterLink>
        </div>
        <div class="nav-content" :class="isActive(PATH_NAME.BID_HISTORY)">
          <RouterLink :to="PATH_NAME.BID_HISTORY">
            <span class="winning-history"></span>
            <div class="label">{{ translate('HEADER_NAV_SUCCESSFUL_BID_HISTORY') }}</div>
          </RouterLink>
        </div>
        <div class="nav-content" :class="isActive(PATH_NAME.MYPAGE_EDIT)">
          <RouterLink :to="PATH_NAME.MYPAGE_EDIT">
            <span class="account"></span>
            <div class="label">{{ translate('MYPAGE_EDIT_PROFILE') }}</div>
            <span class="upload"></span>
          </RouterLink>
        </div>
        <div class="nav-content" :class="isActive(PATH_NAME.MYPAGE_CARD)">
          <RouterLink :to="PATH_NAME.MYPAGE_CARD">
            <span class="card"></span>
            <div class="label">{{ translate('MYPAGE_CARD') }}</div>
          </RouterLink>
        </div>
      </div>
    </div>
  </section>
</template>
