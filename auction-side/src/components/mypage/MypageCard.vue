<script setup lang="ts">
  // import {navigateToPath} from '@/utils'
</script>

<template>
  <main id="main">
    <section id="mypage-form">
      <div class="container">
        <h3>カード情報</h3>

        <form>
          <section id="entry-form">
            <table class="tbl-entry">
              <tbody>
                <tr>
                  <th>カード名義<em class="req">必須</em></th>
                  <td>
                    <div class="ipt-wrap">
                      <input
                        type="text"
                        class="iptW-M"
                        placeholder="例）TARO YAMADA"
                        required
                      /><span class="ipt-rule kome">半角英字</span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <th>カード番号<em class="req">必須</em></th>
                  <td>
                    <input
                      type="text"
                      class="iptW-M"
                      placeholder="例）1234-5678-XXXX-XXXX"
                      required
                    />
                  </td>
                </tr>
                <tr>
                  <th>有効期限<em class="req">必須</em></th>
                  <td>
                    <div class="ipt-wrap">
                      <input type="text" class="iptW-S" placeholder="例）05/28" required /><span
                        class="ipt-rule kome"
                        >半角数字（月/年）</span
                      >
                    </div>
                  </td>
                </tr>
                <tr>
                  <th class="post-code">セキュリティコード<em class="req">必須</em></th>
                  <td>
                    <div class="ipt-wrap">
                      <input type="text" class="iptW-S" placeholder="例）123" required />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </section>
          <div class="btn-form">
            <input type="button" id="sbm-login" class="account-edit" value="入力内容を確認する" />
          </div>
        </form>
      </div>
    </section>
  </main>
</template>
