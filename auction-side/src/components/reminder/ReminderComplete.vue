<script setup>
  import {PATH_NAME} from '@/defined/const'
  import {RouterLink} from 'vue-router'
  import {useLocale} from 'vuetify'
  import BreadCrumb from '../common/BreadCrumb.vue'

  const {t: translate} = useLocale()
</script>
<template>
  <main id="main" class="reminder">
    <BreadCrumb :custom-title="translate('LOGIN_REMINDER_TITLE')" />
    <h2 class="page-ttl">
      <p class="ttl">{{ translate('LOGIN_REMINDER_TITLE') }}</p>
      <p class="sub">{{ translate('LOGIN_REMINDER_SUBTITLE') }}</p>
    </h2>
    <div class="container">
      <div class="remind-msg-comp">
        <p>{{ translate('LOGIN_REMINDER_COMPLETE_MESSAGE') }}</p>
      </div>
      <div class="remind-comp-btn">
        <RouterLink :to="PATH_NAME.LOGIN" class="btnBsc-Black"
          >{{ translate('LOGIN_REMINDER_BACK_TO_LOGIN') }}
        </RouterLink>
      </div>
    </div>
  </main>
</template>
<style scoped src="@/assets/css/home.css" />
