<script setup>
  defineOptions({
    name: 'PasswordReminder',
  })

  import {useReminder} from '@/stores/reminder'
  import {computed, ref} from 'vue'
  import {useLocale} from 'vuetify'
  import useApi from '../../composables/useApi'
  import {PATTERN} from '../../defined/const'
  import BreadCrumb from '../common/BreadCrumb.vue'

  const {apiExecute, parseHtmlResponseError} = useApi()

  const email = ref('')
  const confirmEmail = ref('')
  const reminderStore = useReminder()
  const errorMsg = ref('')
  const {t: translate} = useLocale()

  const checkInput = computed(() => {
    const check = {
      email: {pass: false, errMsg: translate('LOGIN_REMINDER_EMAIL_ERROR')},
      confirmEmail: {
        pass: false,
        errMsg: translate('LOGIN_REMINDER_CONFIRM_EMAIL_ERROR'),
      },
    }
    if (PATTERN.EMAIL.test(email.value)) {
      check.email.pass = true
      check.email.errMsg = ''
    }
    if (PATTERN.EMAIL.test(confirmEmail.value) && email.value === confirmEmail.value) {
      check.confirmEmail.pass = true
      check.confirmEmail.errMsg = ''
    }
    return check
  })

  // const sendRequest = async () => {
  //   const requestData = {user_id: null, email: email.value}
  //   await apiExecute(API_PATH.REISSUE_PASSWORD, requestData)
  //     .then(data => {
  //       reminderStore.completedFlag = true
  //     })
  //     .catch(error => {
  //       errorMsg.value = parseHtmlResponseError(error)?.errorMessage
  //       console.log({errorMsg})
  //     })
  // }

  const sendRequest = async () => {
    // Simulate a fake successful submission
    reminderStore.completedFlag = true
  }
</script>
<template>
  <main id="main" class="reminder">
    <BreadCrumb :customTitle="translate('LOGIN_REMINDER_TITLE')" />
    <h2 class="page-ttl">
      <p class="ttl">{{ translate('LOGIN_REMINDER_TITLE') }}</p>
      <p class="sub">{{ translate('LOGIN_REMINDER_SUBTITLE') }}</p>
    </h2>
    <div class="container">
      <div class="remind-msg">
        <p class="remind-txt-att">
          <span>{{ translate('LOGIN_REMINDER_MESSAGE1') }}</span>
          <span>{{ translate('LOGIN_REMINDER_MESSAGE2') }}</span>
        </p>
        <p class="mt15 remind-txt-att">
          <span
            >{{ translate('LOGIN_REMINDER_FORGOT_ID')
            }}<a href="./inquiry/">{{ translate('LOGIN_REMINDER_CONTACT_LINK') }}</a
            >{{ translate('LOGIN_REMINDER_CONTACT_SUFFIX') }}</span
          >
        </p>
      </div>
      <section id="login-form">
        <form>
          <table class="tbl-login">
            <tbody>
              <tr>
                <th>{{ translate('LOGIN_REMINDER_USER_ID') }}</th>
                <td>
                  <input
                    type="text"
                    class="ime-dis"
                    :placeholder="translate('LOGIN_REMINDER_USER_ID_PLACEHOLDER')"
                    required
                  />
                </td>
              </tr>
              <tr>
                <th>{{ translate('LOGIN_EMAIL') }}</th>
                <td>
                  <input type="email" class="ime-dis" required />
                </td>
              </tr>
              <tr>
                <th>{{ translate('LOGIN_EMAIL_CONFIRM') }}</th>
                <td>
                  <input type="email" class="ime-dis" required />
                </td>
              </tr>
            </tbody>
          </table>
          <div class="btn-form">
            <input
              type="button"
              id="sbm-login"
              :value="translate('LOGIN_REMINDER_SEND_BUTTON')"
              @click="sendRequest"
            />
          </div>
        </form>
      </section>
    </div>
  </main>
</template>

<style scoped>
  .work-break {
    word-break: break-word;
  }
</style>
