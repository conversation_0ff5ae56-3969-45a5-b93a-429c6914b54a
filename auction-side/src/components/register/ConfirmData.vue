<script setup>
  import {format} from 'date-fns'
  import {computed, defineEmits, defineProps, reactive} from 'vue'
  import {useLocale} from 'vuetify'
  import {useRegisterStore} from '../../stores/useRegister'
  import FirstLastName from '../common/FirstLastName.vue'
  import PhoneNumberInput from '../common/PhoneNumberInput.vue'

  const emit = defineEmits(['registMember'])
  const props = defineProps({
    back: {
      type: Function,
      default: () => () => {},
    },
    constants: {
      type: Array,
      default: () => [],
    },
    errorMsg: {
      type: Object,
      default: () => ({}),
    },
    emailLangOptions: {
      type: Array,
      default: () => [],
    },
  })

  const {t: translate, current: locale} = useLocale()
  const {registInputs} = useRegisterStore()

  const loading = reactive({
    companyPostCode: false,
    postCode: false,
    requestSend: false,
  })

  const countryList = computed(() => {
    if (!props.constants) return []
    return props.constants.filter(x => x.key_string === 'COUNTRY_CODE')
  })
  const registMember = computed(() => {
    return registInputs.reduce((result, obj) => {
      result[obj.item] = obj.value
      return result
    }, {})
  })
  const selectedCountry = computed(() => registInputs.find(x => x.item === 'country')?.value)

  const getEmailLangName = emailLang => {
    const title = props.emailLangOptions.find(item => item.value === emailLang)?.title
    return title || ''
  }
</script>
<template>
  <form v-on:submit.prevent>
    <section id="entry-form">
      <p class="entry-form-info">{{ translate('register.subtitleConfirm') }}</p>
      <table class="tbl-entry">
        <tbody>
          <tr
            v-for="input in registInputs.filter(x => x.isVisible(selectedCountry))"
            :key="input.item"
          >
            <th>
              <span class="font-weight-bold" v-html="input.label(translate)"></span>
            </th>
            <td>
              <!-- Select option -->
              <template v-if="input.type === 'select'">
                {{ countryList.find(x => x.value1 === input.value)?.value2 }}
              </template>
              <!-- メール言語設定 -->
              <template v-else-if="input.type === 'emailLang'">
                {{ getEmailLangName(input.value) }}
              </template>
              <!-- Text input -->
              <template v-else-if="input.type === 'text'">
                <!-- Password input -->
                <template v-if="input.item === 'password' || input.item === 'passwordConfirm'">
                  <p>********</p>
                </template>
                <!-- First,lastname input -->
                <template
                  v-else-if="input.item === 'memberName' || input.item === 'memberLastName'"
                >
                  <FirstLastName
                    :country="selectedCountry"
                    :isConfirm="true"
                    :value="{
                      firstName: registInputs.find(x => x.item === 'memberName')?.value,
                      lastName: registInputs.find(x => x.item === 'memberLastName')?.value,
                    }"
                  />
                </template>
                <!-- Tel -->
                <template v-else-if="input.item === 'tel'">
                  <PhoneNumberInput
                    :isConfirm="true"
                    :country="selectedCountry"
                    :value="{
                      countryCode: registInputs.find(x => x.item === 'telCountryCode')?.value,
                      tel: registInputs.find(x => x.item === 'tel')?.value,
                    }"
                  />
                </template>
                <!-- others -->
                <template v-else>
                  {{ input.value }}
                </template>
              </template>
              <!-- Date picker -->
              <template v-else-if="input.type === 'date'">
                {{ format(input.value, translate('register.datePicker.dateFormat')) }}
              </template>
              <!-- チェックボックス -->
              <template v-else-if="input.type === 'checkbox' && input.item === 'personalInfo'">
                {{ translate('register.form.agree') }}
              </template>

              <template v-else>
                <p>{{ input.value === '' ? translate('common.mitaiou') : input.value }}</p>
              </template>

              <!-- エラーメッセージ -->
              <p class="ime-dis iptW-S pb-1 text-red" v-show="errorMsg[input.item]">
                {{ errorMsg[input.item] }}
              </p>
              <p
                class="ime-dis iptW-S pb-1 text-red"
                v-if="errorMsg['emailDuplicated'] && input.item === 'email'"
              >
                {{ errorMsg['emailDuplicated'] }}
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </section>
    <div class="btn-form">
      <input
        class="btn-back"
        type="button"
        :value="translate('common.back')"
        @click="emit('back')"
      />
      <input
        type="button"
        :value="translate('common.send')"
        @click="emit('registMember', registMember, loading.requestSend)"
      />
    </div>
  </form>
</template>
<style scoped>
  #main #entry-form table.tbl-entry input.iptW-M,
  #main #entry-form table.tbl-entry input.iptW-MM {
    background-color: #fff;
  }
</style>
