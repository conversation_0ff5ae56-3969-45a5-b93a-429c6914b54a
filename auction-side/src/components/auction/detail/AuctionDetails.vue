<script setup lang="ts">
  import type {ReactiveProductDetails} from '@/composables/_type'
  import useBid from '@/composables/bid'
  import {formatDateString, priceLocaleString} from '@/composables/common'
  import useFavorite from '@/composables/favorite'
  import useGetItemDetails from '@/composables/getItemDetails'
  import useApi from '@/composables/useApi'
  import useAuctionStatus from '@/composables/useAuctionStatus'
  import {useLanguageRefetch} from '@/composables/useLanguageRefetch.ts'
  import {PATH_NAME} from '@/defined/const'
  import {useTypedI18n} from '@/language/index.ts'
  import {useBidConfirmStore} from '@/stores/bidConfirm'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth'
  import useGoBack, {usePrevRouteStore} from '@/stores/prev-route'
  import {useSearchResultStore} from '@/stores/search-results'
  import {eventBus, navigateToPath} from '@/utils/index.js'
  import {format} from 'date-fns'
  import {
    computed,
    defineAsyncComponent,
    onBeforeMount,
    onMounted,
    onUnmounted,
    ref,
    type Ref,
  } from 'vue'
  import {RouterLink, useRoute, useRouter} from 'vue-router'
  import ImageGallery from './ImageGallery.vue'
  import {productImages} from './utils.ts'

  const BidConfirmModal = defineAsyncComponent(() => import('../../common/BidConfirmModal.vue'))

  const isLoading = ref(true)
  const isManageNo = ref(null)
  // Toggle state for auction classification type
  const auctionType = ref<'ascending' | 'sealed'>('ascending')

  const route = useRoute()
  const auth = useCognitoAuthStore()
  const router = useRouter()
  const {goToPath} = usePrevRouteStore()
  const {apiExecute} = useApi()
  const {getItemDetailByExhItemNo, getConstants} = useGetItemDetails()
  const {t} = useTypedI18n()
  const {refetchOnLanguageChange} = useLanguageRefetch()
  const {goBack} = useGoBack()

  // Additional stores and composables for bidding functionality
  const {productDetails, setProductListForContact} = useSearchResultStore()
  const {setPrevRoute} = usePrevRouteStore()
  const {getAuctionStatusClass} = useAuctionStatus()
  const {increaseFavoriteCount, decreaseFavoriteCount} = useFavorite()
  const bidComposable = useBid()
  const {
    bidHandle,
    addPitch,
    bidPrice: composableBidPrice,
    bidQuantity: composableBidQuantity,
  } = bidComposable
  const bidConfirmStore = useBidConfirmStore()

  // Type assertion for productDetails (since it's a reactive object from store)
  const typedProductDetails = productDetails as ReactiveProductDetails
  console.log(
    '%c ◼️: typedProductDetails ',
    'font-size:16px;background-color:#00ae4b;color:white;',
    typedProductDetails
  )

  // Reactive variables for bidding
  const bidPrice: Ref<string> = ref('')
  const bidQuantity: Ref<string> = ref('1')

  // Toggle function for auction type
  const toggleAuctionType = () => {
    auctionType.value = auctionType.value === 'ascending' ? 'sealed' : 'ascending'
  }

  // 初期表示処理
  const getDetailData = async () => {
    isLoading.value = true
    isManageNo.value = route.params.manageNo

    // ManageNoがない場合は商品一覧に遷移
    // if (!isManageNo.value) {
    //   router.push(PATH_NAME.TOP)
    //   return
    // }

    // delete makeshop logic???
    const isFromMakeshop = !!route.query?.brand_code
    await Promise.all([
      auth.isAuthenticated ? apiExecute('private/get-change-info-constants') : null,
      getConstants(),
      await getItemDetailByExhItemNo(isManageNo.value ?? '', isFromMakeshop ? 'makeshop' : null),
    ])
    if (typedProductDetails?.exhibition_item_no) {
      // Makeshopからアクセスがあった場合、該当の商品コードでオークション中の商品が無い場合は商品一覧に遷移させる
      if (isFromMakeshop && !typedProductDetails.bid_status.can_bid) {
        router.push(PATH_NAME.TOP)
        return
      }
      setProductListForContact()
      // if (auth.isAuthenticated) {
      //   member.setMemberInfo(responses[0])
      // }
      // Initiate bid price when page is loaded
      if (typedProductDetails.bid_status?.bid_price && typedProductDetails.bid_status.pitch_width) {
        bidPrice.value = priceLocaleString(typedProductDetails.bid_status.bid_price)
        // 競り上げの場合は「bidQuantity = ’1’」
        bidQuantity.value = priceLocaleString(
          typedProductDetails.auction_classification === 1
            ? '1'
            : typedProductDetails.bid_status.bid_quantity
        )
      }
    } else {
      router.push(PATH_NAME.TOP)
    }
    isLoading.value = false
  }

  // Product images computed property
  // const productImages = computed(
  //   () =>
  //     typedProductDetails?.images?.map((img: any) => ({
  //       large: img?.src || img || topItemImage,
  //       thumb: img?.src || img || topItemImage,
  //       size: '1200x1200',
  //       alt: img?.alt || typedProductDetails.product_title || 'Product Image',
  //     })) || [{large: topItemImage, thumb: topItemImage, size: '1200x1200', alt: 'Default Product Image'}]
  // )

  // Helper functions
  const getRemainingTime = (endDatetime: string | null | undefined): string => {
    if (!endDatetime) return ''

    const now = new Date()
    const endTime = new Date(endDatetime)
    const diff = endTime.getTime() - now.getTime()

    if (diff <= 0) {
      return '終了'
    }

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `残り${hours}時間${minutes}分`
    } else {
      return `残り${minutes}分`
    }
  }

  const getEndDateTimeDisplay = (endDatetime: string | null | undefined): string => {
    if (!endDatetime) return ''

    const result = formatDateString(endDatetime)
    if (!result) return ''

    const {datePart, timePart} = result
    return `（${datePart} ${timePart} 終了予定）`
  }

  const incrementBidPrice = (currentPrice: string | undefined, increment: number): void => {
    const current = parseInt(currentPrice?.replace(/[^0-9]/g, '') || '0')
    bidPrice.value = (current + increment).toLocaleString()
  }

  // Handle bid price increment using the composable function
  const handleBidPriceIncrement = (increment: number): void => {
    const currentPrice = typedProductDetails.bid_status?.current_price || 0
    const pitchWidth = typedProductDetails.bid_status?.pitch_width || 1
    addPitch(currentPrice, increment, pitchWidth)
    // Sync the local bidPrice with the composable's bidPrice
    bidPrice.value = composableBidPrice.value
  }

  // Handle bid button click - now shows confirmation modal instead of direct bid
  const handleBidClick = (): void => {
    // Sync local values with composable values
    if (!bidPrice.value && !composableBidPrice.value) {
      console.log('🔵 No bid price entered!')
      return
    }

    // Use composable values if available, otherwise use local values
    const finalBidPrice = composableBidPrice.value || bidPrice.value
    const finalBidQuantity = composableBidQuantity.value || bidQuantity.value || '1'

    // Sync the composable values with our current values
    composableBidPrice.value = finalBidPrice
    composableBidQuantity.value = finalBidQuantity

    const bidParams = {
      exhibitionNo: typedProductDetails.exhibition_no || '',
      exhibitionItemNo: typedProductDetails.exhibition_item_no || '',
      exhibitionName:
        typedProductDetails.productName || typedProductDetails.freeFields?.product_name || '',
      lowestBidPrice: typedProductDetails.bid_status?.lowest_bid_price || 0,
      lowestBidQuantity: typedProductDetails.bid_status?.lowest_bid_quantity || 1,
      pitchWidth: typedProductDetails.bid_status?.pitch_width || 1,
      freeField: typedProductDetails.free_field || typedProductDetails.freeFields || {},
      newBidPrice: finalBidPrice,
      newBidQuantity: finalBidQuantity,
      currentBidPrice: typedProductDetails.bid_status?.current_price || 0,
      enteredBidPrice: typedProductDetails.bid_status?.bid_price || 0,
      enteredBidQuantity: typedProductDetails.bid_status?.bid_quantity || 0,
      maxQuantity: typedProductDetails.bid_status?.quantity || 0,
      isAscendingAuction: typedProductDetails.auction_classification === 1,
      hasUserBid: typedProductDetails.hasUserBid,
    }

    // Use bidHandle from composable which shows the confirmation modal
    bidHandle(bidParams)
  }

  // Toggle favorite function
  const toggleFavorite = (exhibitionItemNo: string | undefined): void => {
    if (!exhibitionItemNo) return

    if (typedProductDetails.attention_info?.is_favorited) {
      decreaseFavoriteCount(exhibitionItemNo)
    } else {
      increaseFavoriteCount(exhibitionItemNo)
    }
  }

  // Handle refresh after bid confirmation modal closes
  const handleRefresh = (classification?: any): void => {
    // Refresh the product details or perform any necessary updates
    console.log('Refreshing after bid confirmation', classification)
    // You can add specific refresh logic here if needed
  }

  // TODO, use name from database
  const getFieldDisplayName = (key: string): string => {
    const fieldNameMap: Record<string, string> = {
      model: '型番',
      itemConditionNote: '未使用品',
      shippingMethod: '配送方法',
      shippingFee: '送料',
      rankB: 'ランクB',
      rankC: 'ランクC',
      newsprc: 'NEWS価格',
    }
    return fieldNameMap[key] || key
  }

  const customizedFieldsArray = computed(() => {
    if (!typedProductDetails?.customizedFields) return []
    if (Array.isArray(typedProductDetails.customizedFields)) {
      return typedProductDetails.customizedFields
    }
    return Object.entries(typedProductDetails.customizedFields).map(([key, value]) => ({
      key,
      value,
    }))
  })

  const handleAfterBidSuccess = async bidSuccessPrice => {
    console.log('bidSuccessPrice: ', bidSuccessPrice)
  }

  const handleUpdate = async () => {
    await getDetailData()
  }

  const formattedDescription = computed(() => {
    const raw = typedProductDetails?.freeFields?.description || '商品説明がありません。'
    return raw.replace(/\n/g, '<br>')
  })

  onBeforeMount(async () => {
    await getDetailData()
  })

  onMounted(() => {
    eventBus.on('onBidSuccess', (bidItemNo, bidSuccessPrice) => {
      if (typedProductDetails.exhibition_item_no === bidItemNo) {
        handleAfterBidSuccess(bidSuccessPrice)
      }
    })
    1
    // Set up language change watcher
    refetchOnLanguageChange(getDetailData)
  })

  onUnmounted(() => {
    eventBus.off('onBidSuccess', handleAfterBidSuccess)
  })
</script>

<template>
  <main id="main">
    <div id="pNav">
      <ul>
        <li><RouterLink :to="PATH_NAME.TOP">TOP</RouterLink></li>
        <li>
          <RouterLink :to="PATH_NAME.SEARCH_RESULTS">{{
            typedProductDetails?.freeFields?.category || 'カテゴリ'
          }}</RouterLink>
        </li>
        <li>
          {{
            typedProductDetails?.productName || typedProductDetails?.freeFields?.product_name || ''
          }}
        </li>
      </ul>
    </div>
    <section id="item-detail">
      <div class="back">
        <button @click="goBack" class="text">一覧に戻る</button>
      </div>
      <div class="container">
        <p class="item-name-wrap">
          <span class="name">
            {{ typedProductDetails?.freeFields?.product_name }}
          </span>
          <span class="tag_status">新品未使用</span>
        </p>
        <ul class="tab-wrap">
          <li class="tab-main">未使用</li>
          <li class="tab-sub">新品</li>
          <li class="tab-wari">付属品あり</li>
        </ul>
        <div id="item-data">
          <div class="item_d-main">
            <div class="item_d-main-visual">
              <ImageGallery
                :images="productImages"
                gallery-class="my-gallery"
                slider-main-class="slider-for"
                slider-nav-class="slider-nav"
                :nav-slides-to-show="7"
                :nav-responsive="[{breakpoint: 767, settings: {slidesToShow: 4}}]"
              />

              <div class="item_d-bid-history only_pc">
                <p>入札履歴</p>
                <table cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <th class="time">最終入札時刻</th>
                      <th class="amount">入札額</th>

                      <th class="bidder"></th>
                    </tr>
                    <tr>
                      <td class="time">
                        <span>7月11日</span>
                        <span>11時28分</span>
                      </td>
                      <td class="amount">230,600円</td>

                      <td class="bidder">
                        <span class="highest">最高入札額</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="time"><span>7月11日</span><span>10時34分</span></td>
                      <td class="amount">124,600円</td>

                      <td class="bidder"></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="item_d-main-txt">
              <div class="item_d-main-data">
                <div class="bid-mode">
                  <p class="mode-name">
                    <span>
                      {{
                        typedProductDetails?.auction_classification === 1
                          ? '競り上がり式オークション'
                          : '封印入札式オークション'
                      }}
                    </span>
                  </p>
                  <p class="update" @click="handleUpdate"><span>更新</span></p>
                </div>
                <dl class="bid-price">
                  <dd class="price-start">
                    <span class="label">スタート価格</span>
                    <span class="value">
                      {{ priceLocaleString(typedProductDetails?.bid_status?.lowest_bid_price) }}
                      <span class="unit">円</span>
                    </span>
                  </dd>
                  <dd class="price-now">
                    <span class="label">現在価格</span>
                    <span class="value">
                      {{ priceLocaleString(typedProductDetails?.bid_status?.current_price) }}
                      <span class="unit">円</span>
                    </span>
                  </dd>
                  <dd class="tax-include">
                    <span class="label">税込価格</span>
                    <span class="value">44,000<span class="unit">円</span></span>
                  </dd>
                  <dd class="price-buyit">
                    <span class="label">即決価格</span>
                    <span class="value" v-if="typedProductDetails?.freeFields?.instant_price">
                      {{ priceLocaleString(typedProductDetails?.freeFields?.instant_price) }}
                      <span class="unit">円</span>
                    </span>
                  </dd>
                </dl>
                <div class="bid-status">
                  <span class="extended" v-if="typedProductDetails?.bid_status?.extending">
                    {{ t('BID_STATUS_EXTENDING') }}
                  </span>
                  <span class="top" v-if="typedProductDetails?.bid_status?.is_top_member">
                    {{ t('YOU_ARE_TOP') }}
                  </span>
                  <span
                    class="extended"
                    v-if="new Date(typedProductDetails?.bid_status?.end_datetime) < new Date()"
                  >
                    {{ t('BID_STATUS_ENDED') }}
                  </span>
                </div>
              </div>
              <div class="item_d-bid-history only_sp">
                <p>入札履歴</p>
                <table cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <th class="time">最終入札時刻</th>
                      <th class="amount">入札額</th>

                      <th class="bidder"></th>
                    </tr>
                    <tr>
                      <td class="time">
                        <span>7月11日</span>
                        <span>11時28分</span>
                      </td>
                      <td class="amount">230,600円</td>

                      <td class="bidder">
                        <span class="highest">最高入札額</span>
                      </td>
                    </tr>
                    <tr>
                      <td class="time"><span>7月11日</span><span>10時34分</span></td>
                      <td class="amount">124,600円</td>

                      <td class="bidder"></td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div
                class="place-bid"
                :class="typedProductDetails?.bid_status?.can_bid === false ? 'view_only' : ''"
              >
                <dl class="bidded-price">
                  <dt>入札済み価格</dt>
                  <dd>
                    <span class="price">
                      {{ priceLocaleString(typedProductDetails?.bid_status?.bid_price) }}
                      <span class="unit">円</span>
                    </span>
                  </dd>
                </dl>
                <div class="bid_head">
                  <p class="ttl"><span>入札価格</span></p>
                  <p class="price">
                    <input
                      type="text"
                      data-id="price-bid"
                      v-model="bidPrice"
                      class="price-bid"
                      :placeholder="
                        priceLocaleString(typedProductDetails?.bid_status?.current_price) || '1,000'
                      "
                      @input="composableBidPrice = bidPrice"
                    /><span class="unit">円</span>
                  </p>
                </div>
                <ul class="bid-unit-wrap">
                  <li>
                    <button class="bid-unit" @click="handleBidPriceIncrement(10000)">
                      <span class="icn_add"></span>10,000円
                    </button>
                  </li>
                  <li>
                    <button class="bid-unit" @click="handleBidPriceIncrement(50000)">
                      <span class="icn_add"></span>50,000円
                    </button>
                  </li>
                  <li class="last">
                    <button class="bid-unit" @click="handleBidPriceIncrement(100000)">
                      <span class="icn_add"></span>100,000円
                    </button>
                  </li>
                </ul>
                <p class="note">※単位で入札できます。</p>
                <div class="btn-wrap">
                  <button class="btn modal-open" @click="handleBidClick" :disabled="!bidPrice">
                    <img class="pct" src="@/assets/img/common/icn_bid_detail.svg" /><span
                      class="bid-text"
                      >入札する</span
                    >
                  </button>
                  <button
                    class="btn chat"
                    @click="navigateToPath('/details/chat/4', goToPath, $route)"
                  >
                    <img class="pct" src="@/assets/img/common/icn_chat_detail.svg" />
                    <span class="text">チャットで質問する</span>
                  </button>
                  <a class="view_comment">質問コメントを見る</a>
                </div>

                <div class="current-status">
                  <div class="end-date">
                    <img src="@/assets/img/common/icn_clock_list.png" />
                    <div class="end-l">終了予定</div>
                    <div class="end-v">
                      <span>{{
                        typedProductDetails?.bid_status?.end_datetime
                          ? format(typedProductDetails.bid_status.end_datetime, 'MM月dd日')
                          : ''
                      }}</span
                      ><span>{{
                        typedProductDetails?.bid_status?.end_datetime
                          ? format(typedProductDetails.bid_status.end_datetime, 'HH時mm分')
                          : ''
                      }}</span>
                    </div>
                  </div>
                  <div class="other-info">
                    <div class="view">
                      <img src="@/assets/img/common/icn_eye_list.svg" /><span>{{
                        typedProductDetails?.attention_info?.view_count
                      }}</span>
                    </div>
                    <div class="favorite">
                      <img src="@/assets/img/common/icn_favorite_detail.svg" /><span>{{
                        typedProductDetails?.attention_info?.favorited_count
                      }}</span>
                    </div>
                    <div class="bid">
                      <img src="@/assets/img/common/icn_bid.svg" /><span>{{
                        typedProductDetails?.attention_info?.bid_count
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="com-item-box">
                <div id="sns-share">
                  <p>この商品をシェア</p>
                  <ul>
                    <li>
                      <a href="A"
                        ><img
                          src="@/assets/img/common/icn_sns_facebook.svg"
                          alt="Facebook"
                          class="facebook"
                      /></a>
                    </li>
                    <li>
                      <a href="A"
                        ><img src="@/assets/img/common/icn_sns_x.svg" alt="twitter" class="x"
                      /></a>
                    </li>
                    <li>
                      <a href="A"
                        ><img
                          src="@/assets/img/common/icn_sns_instagram.svg"
                          alt="Instagram"
                          class="instagram"
                      /></a>
                    </li>
                  </ul>
                </div>
                <p
                  class="fav-mark"
                  @click="toggleFavorite(typedProductDetails?.exhibition_item_no)"
                >
                  <span>お気に入り</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="item-note">
          <h2>商品説明</h2>
          <div class="contents-wrap">
            <h3>ご購入前にご確認ください</h3>
            <div v-html="formattedDescription"></div>
            <table class="spec">
              <tbody>
                <tr class="">
                  <th>商品名</th>
                  <td>
                    {{ typedProductDetails?.freeFields?.product_name }}
                  </td>
                </tr>
                <tr v-for="field in customizedFieldsArray" :key="field.key">
                  <th>{{ getFieldDisplayName(field.key) }}</th>
                  <td v-html="field.value"></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <BidConfirmModal
        v-model="bidConfirmStore.showBidConfirm"
        :isAscendingAuction="typedProductDetails?.auction_classification === 1"
        @refresh="handleRefresh"
      />
    </section>
  </main>
</template>

<style lang="css" scoped>
  .thumbnail-item {
    padding: 0 3px !important;
  }

  .auction-type-switch {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 0;
    margin-bottom: 2rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .switch-label {
    margin-right: 1rem;
    font-weight: 500;
    color: #333;
    font-size: 1rem;
  }

  .switch-buttons {
    display: flex;
    background-color: #fff;
    border: 2px solid #427fae;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(66, 127, 174, 0.1);
  }

  .switch-btn {
    padding: 0.5rem 1.5rem;
    border: none;
    background-color: #fff;
    color: #427fae;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }

  .switch-btn:hover:not(:disabled) {
    background-color: rgba(66, 127, 174, 0.1);
  }

  .switch-btn.is-active {
    background-color: #427fae;
    color: #fff;
    cursor: default;
  }

  .switch-btn:disabled {
    cursor: default;
  }

  /* Responsive design */
  @media screen and (max-width: 767px) {
    .auction-type-switch {
      flex-direction: column;
      align-items: center;
      padding: 1rem;
    }

    .switch-label {
      margin-right: 0;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .switch-btn {
      padding: 0.4rem 1rem;
      font-size: 0.8rem;
    }
  }

  .view_only:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(240, 240, 240, 0.5);
    z-index: 10;
  }

  .back button.text {
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    color: inherit;
    text-decoration: underline;
    cursor: pointer;
    outline: none;
  }

  .back button.text:hover {
    text-decoration: none;
  }
</style>
