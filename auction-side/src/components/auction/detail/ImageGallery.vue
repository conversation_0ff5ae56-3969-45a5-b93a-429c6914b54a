<script setup>
  import $ from 'jquery'
  import {defineProps, onMounted, onUnmounted} from 'vue'

  // Props interface
  const props = defineProps({
    images: {
      type: Array,
      required: true,
      validator: images => {
        return images.every(img => typeof img === 'object' && img.large && img.thumb && img.size)
      },
    },
    galleryClass: {
      type: String,
      default: 'my-gallery',
    },
    sliderMainClass: {
      type: String,
      default: 'slider-for',
    },
    sliderNavClass: {
      type: String,
      default: 'slider-nav',
    },
    slideItemClass: {
      type: String,
      default: 'slide-item',
    },
    thumbnailItemClass: {
      type: String,
      default: 'thumbnail-item',
    },
    slidesToShow: {
      type: Number,
      default: 7,
    },
    slidesToShowMobile: {
      type: Number,
      default: 4,
    },
    mobileBreakpoint: {
      type: Number,
      default: 767,
    },
    initDelay: {
      type: Number,
      default: 300,
    },
  })

  // Load PhotoSwipe dynamically from public directory
  const loadPhotoSwipe = () => {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (window.PhotoSwipe && window.PhotoSwipeUI_Default) {
        resolve()
        return
      }

      // Load PhotoSwipe core first
      const photoSwipeScript = document.createElement('script')
      photoSwipeScript.src = '/js/gallery/photoswipe.min.js'
      photoSwipeScript.onload = () => {
        // Then load PhotoSwipe UI
        const photoSwipeUIScript = document.createElement('script')
        photoSwipeUIScript.src = '/js/gallery/photoswipe-ui-default.js'
        photoSwipeUIScript.onload = () => resolve()
        photoSwipeUIScript.onerror = () => reject(new Error('Failed to load PhotoSwipe UI'))
        document.head.appendChild(photoSwipeUIScript)
      }
      photoSwipeScript.onerror = () => reject(new Error('Failed to load PhotoSwipe'))
      document.head.appendChild(photoSwipeScript)
    })
  }

  // Gallery initialization function
  const initializeGallery = async () => {
    try {
      // Initialize Slick sliders first
      $(`.${props.sliderMainClass}`).slick({
        asNavFor: `.${props.sliderNavClass}`,
        autoplay: false,
        arrows: true,
        infinite: true,
      })

      $(`.${props.sliderNavClass}`).slick({
        slidesToShow: props.slidesToShow,
        slidesToScroll: 1,
        asNavFor: `.${props.sliderMainClass}`,
        focusOnSelect: true,
        responsive: [
          {
            breakpoint: props.mobileBreakpoint,
            settings: {
              slidesToShow: props.slidesToShowMobile,
            },
          },
        ],
      })

      // Load PhotoSwipe and initialize
      await loadPhotoSwipe()

      // Initialize PhotoSwipe
      const $gallery = document.querySelector(`.${props.galleryClass}`)
      const $pswpElement = document.querySelector('.pswp')

      if (!$gallery || !$pswpElement) {
        console.warn('Gallery elements not found')
        return
      }

      if (!window.PhotoSwipe || !window.PhotoSwipeUI_Default) {
        console.warn('PhotoSwipe not loaded')
        return
      }

      const items = Array.prototype.map
        .call($gallery.querySelectorAll('figure:not(.slick-cloned)'), $slide => {
          const $a = $slide.querySelector('a')

          // Skip if no anchor tag found
          if (!$a) {
            console.warn('No anchor tag found in slide item')
            return null
          }

          const sizeAttr = $a.getAttribute('data-size')

          // Provide default size if data-size is missing or null
          const size = sizeAttr ? sizeAttr.split('x') : ['1200', '1200']

          return {
            src: $a.getAttribute('href'),
            w: parseInt(size[0], 10),
            h: parseInt(size[1], 10),
            el: $slide,
          }
        })
        .filter(item => item !== null) // Filter out any null items

      // Check if we have any valid items
      if (items.length === 0) {
        console.warn('No valid gallery items found')
        return
      }

      const openPhotoSwipe = index => {
        const options = {
          index,
          history: false,
          getThumbBoundsFn(index) {
            const thumbnail = items[index].el.querySelector('img')
            const pageYScroll = window.pageYOffset || document.documentElement.scrollTop
            const elRect = thumbnail.getBoundingClientRect()

            return {
              x: elRect.left,
              y: elRect.top + pageYScroll,
              w: elRect.width,
            }
          },
        }

        const gallery = new window.PhotoSwipe(
          $pswpElement,
          window.PhotoSwipeUI_Default,
          items,
          options
        )
        gallery.init()
      }

      // Add click event listener for PhotoSwipe
      const slickList = $gallery.querySelector('.slick-list')
      if (slickList) {
        slickList.addEventListener('click', event => {
          event.preventDefault()
          const index = $(`.${props.sliderMainClass}`).slick('slickCurrentSlide')
          openPhotoSwipe(index)
        })
      }

      console.log('Gallery initialized successfully with PhotoSwipe')
    } catch (error) {
      console.error('Failed to initialize gallery:', error)
      // Fallback: simple lightbox functionality
      const $gallery = document.querySelector(`.${props.galleryClass}`)
      if ($gallery) {
        $gallery.addEventListener('click', event => {
          const slideItem = event.target.closest(`.${props.slideItemClass}`)
          if (slideItem) {
            event.preventDefault()
            const link = slideItem.querySelector('a')
            if (link) {
              window.open(link.href, '_blank')
            }
          }
        })
      }
    }
  }

  // Cleanup function
  const destroyGallery = () => {
    // Destroy Slick sliders if they exist
    if ($(`.${props.sliderMainClass}`).hasClass('slick-initialized')) {
      $(`.${props.sliderMainClass}`).slick('unslick')
    }
    if ($(`.${props.sliderNavClass}`).hasClass('slick-initialized')) {
      $(`.${props.sliderNavClass}`).slick('unslick')
    }
  }

  // Vue lifecycle hooks
  onMounted(() => {
    // Wait for DOM to be fully rendered, then initialize gallery
    initializeGallery()
  })

  onUnmounted(() => {
    destroyGallery()
  })
</script>

<template>
  <div class="product-gallery">
    <div class="slider_wrap">
      <div :class="[galleryClass, sliderMainClass]">
        <figure :class="slideItemClass" v-for="(image, index) in images" :key="index">
          <a :href="image.large" :data-size="image.size">
            <img :src="image.large" :alt="image.alt || 'Product Image'" />
          </a>
        </figure>
      </div>
      <ul :class="sliderNavClass">
        <li :class="thumbnailItemClass" v-for="(image, index) in images" :key="index">
          <img :src="image.thumb" :alt="image.alt || 'Product Thumbnail'" />
        </li>
      </ul>
    </div>

    <!-- PhotoSwipe Lightbox -->
    <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="pswp__bg"></div>
      <div class="pswp__scroll-wrap">
        <div class="pswp__container">
          <div class="pswp__item"></div>
          <div class="pswp__item"></div>
          <div class="pswp__item"></div>
        </div>
        <div class="pswp__ui pswp__ui--hidden">
          <div class="pswp__top-bar">
            <div class="pswp__counter"></div>
            <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
            <div class="pswp__preloader">
              <div class="pswp__preloader__icn">
                <div class="pswp__preloader__cut">
                  <div class="pswp__preloader__donut"></div>
                </div>
              </div>
            </div>
          </div>
          <button
            class="pswp__button pswp__button--arrow--left"
            title="Previous (arrow left)"
          ></button>
          <button
            class="pswp__button pswp__button--arrow--right"
            title="Next (arrow right)"
          ></button>
          <div class="pswp__caption">
            <div class="pswp__caption__center"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="css" scoped>
  .thumbnail-item {
    padding: 0 3px !important;
  }

  .product-gallery {
    width: 100%;
  }

  .slider_wrap {
    width: 100%;
  }
</style>
