<script setup>
  import VueCountdown from '@chenfengyuan/vue-countdown'
  import {computed} from 'vue'
  import {useLocale} from 'vuetify'

  const props = defineProps(['remainingSeconds'])
  const emit = defineEmits(['countdown-ended'])
  const {t, current} = useLocale()

  console.log('remainingSeconds: ', props.remainingSeconds)
  const remainingTime = computed(() => {
    return (props.remainingSeconds || 0) * 1000
  })

  const onCountdownEnd = () => {
    emit('countdown-ended')
  }
</script>

<template>
  <VueCountdown
    :time="remainingTime"
    @end="onCountdownEnd"
    v-slot="{days, hours, minutes, seconds}"
  >
    <div v-if="days > 0" class="d-inline-flex" :class="{'add-space': current === 'en'}">
      <span class="countdown-item">{{ days }}</span
      >{{ t('common.day') }}
    </div>
    <div v-if="hours > 0" class="d-inline-flex" :class="{'add-space': current === 'en'}">
      <span class="countdown-item">{{ hours }}</span
      >{{ t('common.hour') }}
    </div>
    <div v-if="minutes" class="d-inline-flex" :class="{'add-space': current === 'en'}">
      <span class="countdown-item">{{ minutes }}</span
      >{{ t('common.minute') }}
    </div>
    <div class="d-inline-flex" :class="{'add-space': current === 'en'}">
      <span class="countdown-item">{{ seconds }}</span
      >{{ t('common.second') }}
    </div>
  </VueCountdown>
</template>

<style scoped lang="scss">
  div.fade {
    animation: blink 1s ease-in-out infinite alternate;
    text-align: center;
  }

  @keyframes blink {
    from {
      color: #7d7d7d;
    }
    to {
      color: red;
    }
  }

  .countdown-item {
    border-radius: 3px;
  }
  .add-space {
    margin-right: 3px;
  }
</style>
