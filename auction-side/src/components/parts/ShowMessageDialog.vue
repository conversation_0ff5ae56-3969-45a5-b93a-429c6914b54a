<script setup>
  import {useLocale} from 'vuetify'
  import {useMessageDialogStore} from '../../stores/messag-dialog'
  import ModalDialog from '../common/ModalDialog.vue'

  const dialog = useMessageDialogStore()
  const {t: translate} = useLocale()
</script>
<template>
  <ModalDialog v-model="dialog.showMessageDialog" :showTopCloseButton="dialog.showTopCloseBtn">
    <div class="d-flex flex-column ga-10">
      <p class="text-center text-h5 note dialog_warning-message">
        <span style="white-space: pre-wrap">{{ dialog.message }}</span>
      </p>
      <div class="text-center button-wrap">
        <v-btn
          v-if="dialog.showCancelBtn"
          class="mx-1 button-wrap-button close-btn"
          variant="outlined"
          size="large"
          @click="dialog.handleClose"
          >{{ translate('AUTH_CANCEL') }}
        </v-btn>
        <v-btn
          v-if="dialog.showCloseBtn"
          class="mx-1 button-wrap-button close-btn"
          variant="outlined"
          size="large"
          @click="dialog.handleClose"
          >{{ translate('AUTH_CLOSE') }}
        </v-btn>
        <v-btn
          v-if="dialog.showOKBtn"
          class="mx-1 button-wrap-button"
          variant="flat"
          color="info"
          size="large"
          @click="dialog.handleClickedOk"
          >OK
        </v-btn>
        <v-btn
          v-if="dialog.showLogoutBtn"
          class="mx-1 button-wrap-button"
          variant="flat"
          color="info"
          size="large"
          @click="dialog.handleLogout"
          >{{ translate('AUTH_LOGOUT') }}
        </v-btn>
        <v-btn
          v-if="dialog.showConfirmBtn"
          class="mx-1 button-wrap-button"
          variant="flat"
          color="info"
          size="large"
          @click="dialog.handleConfirm"
          >{{ translate('COMMON_CONFIRM') }}
        </v-btn>
      </div>
    </div>
  </ModalDialog>
</template>
