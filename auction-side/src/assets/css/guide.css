@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ガイド
 * *********************************************************************** */
#main #guide {
  padding-bottom: 60px;
}
@media screen and (max-width: 767px) {
  #main #guide {
    padding-bottom: 40px;
  }
}
#main #guide ul.step {
  padding-top: 10px;
}
#main #guide ul.step > li {
  margin-top: 30px;
  background-color: #F7F7F7;
  position: relative;
}
#main #guide ul.step > li::before {
  content: "";
  display: block;
  position: absolute;
  top: 70px;
  bottom: 20px;
  left: 48px;
  width: 34px;
  height: auto;
  background-color: #427fae;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li::before {
    display: none;
  }
}
#main #guide ul.step > li::after {
  content: "";
  display: block;
  position: absolute;
  bottom: -20px;
  left: 35px;
  border-top: 40px solid #427fae;
  border-right: 30px solid transparent;
  border-left: 30px solid transparent;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li::after {
    left: 50%;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    border-top-width: 30px;
  }
}
#main #guide ul.step > li:last-of-type::before, #main #guide ul.step > li:last-of-type::after {
  display: none;
}
#main #guide ul.step > li .conts {
  padding: 40px 40px 40px 160px;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li .conts {
    padding: 20px 15px 35px;
  }
}
#main #guide ul.step > li h3 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 70px;
  margin: 1rem 0 2rem;
  font-weight: 700;
  font-size: 22px;
  border-bottom: 2px solid #EAEAEA;
  position: relative;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li h3 {
    height: auto;
    min-height: 55px;
    margin: 1rem 0;
    padding: 10px 15px 10px 0;
    font-size: 18px;
  }
}
#main #guide ul.step > li h3::after {
  content: "";
  display: block;
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 130px;
  height: 2px;
  background-color: #427fae;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li h3::after {
    width: 100px;
  }
}
#main #guide ul.step > li h3 em {
  display: block;
  width: 130px;
  font-weight: 700;
  color: #427fae;
  text-align: center;
  margin-right: 30px;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li h3 em {
    width: 100px;
    margin-right: 15px;
  }
}
#main #guide ul.step > li .outline {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 18px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li .outline {
    display: block;
    font-size: 1rem;
  }
}
#main #guide ul.step > li .outline .out-txt {
  -ms-flex-item-align: start;
      -ms-grid-row-align: start;
      align-self: start;
}
#main #guide ul.step > li.step-1 .outline .out-txt {
  padding-top: 10px;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li.step-1 .outline .out-txt {
    padding-top: 0;
  }
}
#main #guide ul.step > li .outline .out-txt p.att {
  text-indent: -2em;
  padding-left: 2em;
}
#main #guide ul.step > li .outline .out-img {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  margin: 0 40px 0 80px;
  -ms-flex-item-align: start;
      -ms-grid-row-align: start;
      align-self: start;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li .outline .out-img {
    margin-left: 0;
    margin: 20px 0 0;
    text-align: center;
  }
}
#main #guide ul.step > li .outline .out-img img {
  max-width: 180px;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li .outline .out-img img {
    width: 45%;
  }
}
#main #guide ul.step > li.step-1 .btn-signup {
  width: 100%;
  max-width: 370px;
  margin: 30px auto 0;
}
#main #guide ul.step > li.step-1 .btn-signup a span {
  font-weight: 700;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li.step-1 .btn-signup a span {
    font-size: 12px;
  }
}
#main #guide ul.step > li.step-1 .judge {
  background-color: #fff;
  margin-top: 40px;
  padding: 30px 10px 30px 40px;
}
@media screen and (max-width: 767px) {
  #main #guide ul.step > li.step-1 .judge {
    padding: 20px;
  }
}

/* ---------------------------
 * *STEP2
 * *----------------------------- */
/* タブ */
main #guide ul.step > li.step-2 #tab_btn {
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
main #guide ul.step > li.step-2 #tab_btn li {
  width: 33.1%;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_btn li {
    width: 32%;
  }
}
main #guide ul.step > li.step-2 #tab_btn li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  height: 60px;
  background-color: #EAEAEA;
  color: #427fae;
  font-size: 18px;
  font-weight: 700;
  text-decoration: none;
  position: relative;
  border-radius: 20px 20px 0 0;
  padding: 5px 25px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_btn li a {
    border-radius: 10px 10px 0 0;
    padding: 10px;
    height: 100%;
    font-size: 13px;
    line-height: 1.4;
  }
}
main #guide ul.step > li.step-2 #tab_btn li a:hover {
  opacity: 1;
}
main #guide ul.step > li.step-2 #tab_btn li.active a {
  background-color: #427fae;
  color: #fff;
}
main #guide ul.step > li.step-2 #tab_info > li {
  border: 2px solid #427fae;
  background-color: #fff;
  padding: 40px;
  font-size: 18px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li {
    padding: 1rem;
  }
}
main #guide ul.step > li.step-2 #tab_info > li h4 {
  font-weight: 700;
  color: #427fae;
  margin-top: 40px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li h4 {
    margin-top: 30px;
  }
}
main #guide ul.step > li.step-2 #tab_info > li h4:first-of-type {
  margin-top: 0;
}
main #guide ul.step > li.step-2 #tab_info > li h4 + p {
  margin-top: 5px;
}
main #guide ul.step > li.step-2 #tab_info > li p.assist-h4 {
  font-size: 22px;
  font-weight: 700;
  margin-top: 50px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li p.assist-h4 {
    margin-top: 40px;
    font-size: 20px;
  }
}
main #guide ul.step > li.step-2 #tab_info > li p.assist-h4 + h4 {
  margin-top: 20px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li p.assist-h4 + h4 {
    margin-top: 10px;
  }
}
main #guide ul.step > li.step-2 #tab_info > li a.btn-list {
  margin: 40px 0 0;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li a.btn-list {
    margin: 20px 0 0;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .schedule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-top: 15px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li .schedule {
    display: block;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .schedule figure {
  width: 340px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li .schedule figure {
    width: 100%;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .schedule .schedule_txt {
  width: 500px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li .schedule .schedule_txt {
    width: 100%;
    margin-top: 20px;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .schedule .schedule_txt .schedule_btn {
  width: 100%;
  max-width: 370px;
  margin: 15px auto 0;
}
main #guide ul.step > li.step-2 #tab_info > li p.method {
  font-size: 23px;
  font-weight: 700;
  color: #427fae;
  background-color: #fef6f6;
  border: 1px solid #427fae;
  border-radius: 100vh;
  padding: 10px 20px;
  text-align: center;
  margin-top: 15px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li p.method {
    border-radius: 10px;
    font-size: 18px;
    padding: 10px 15px;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .bid-graph {
  margin-top: 20px;
  border: 1px solid #E3E3E3;
  text-align: center;
  padding: 30px 5px;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li .bid-graph {
    padding: 20px 10px 10px;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .bid-graph img {
  display: inline-block;
  margin-top: 20px;
}
main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-txt {
  text-align: center;
  font-weight: 700;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-txt {
    font-size: 0.9rem;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-fds {
  border: 2px solid #E3E3E3;
  border-radius: 20px;
  width: 800px;
  max-width: calc(100% - 1.6rem);
  margin: 15px auto 0;
  font-size: 0.9rem;
  text-align: center;
  padding: 15px;
  position: relative;
}
@media screen and (max-width: 767px) {
  main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-fds {
    width: 100%;
    max-width: calc(100% - 0.4rem);
    margin: 20px auto 1rem;
    font-size: 14px;
    text-align: left;
    border-radius: 10px;
    padding: 10px 15px;
  }
}
main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-fds::before, main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-fds::after {
  content: "";
  display: block;
  position: absolute;
  right: 50%;
  -webkit-transform: translateX(50%);
          transform: translateX(50%);
}
main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-fds::before {
  border-right: 10px solid transparent;
  border-bottom: 16px solid #E3E3E3;
  border-left: 10px solid transparent;
  top: -16px;
}
main #guide ul.step > li.step-2 #tab_info > li .bid-graph p.ex-fds::after {
  border-right: 10px solid transparent;
  border-bottom: 16px solid #fff;
  border-left: 10px solid transparent;
  top: -13px;
}
/*# sourceMappingURL=guide.css.map */