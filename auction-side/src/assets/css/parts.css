@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* アニメーション
 * *========================================== */
@-webkit-keyframes fade-basic {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fade-basic {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ボタン
 * *********************************************************************** */
/* 基本形ボタン
 * *========================================== */
[class^=btnBsc-] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 60px;
  border-radius: 100vh;
  margin: 0 auto;
  padding: 0;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  width: 100%;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
@media screen and (max-width: 767px) {
  [class^=btnBsc-] {
    height: 50px;
    font-size: 14px;
  }
}
[class^=btnBsc-]:hover {
  opacity: 0.8;
}
[class^=btnBsc-] img {
  display: inline-block;
}

/* リスト下ボタン */
main section .wrap-btn {
  width: 100%;
  margin: 3rem 0;
  text-align: center;
}
main section .wrap-btn .list-more {
  width: 300px;
  height: 50px;
  margin: 0 auto;
  padding: 0.5rem 2rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  background-color: #bf2a24;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  main section .wrap-btn .list-more {
    height: 60px;
  }
}

/* 退会ボタン */
.container .wrap-btn {
  margin: 0 0 60px;
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  .container .wrap-btn {
    margin: 14vw 0 7vw;
    padding: 0;
  }
}
.container .wrap-btn button.withdraw {
  width: 280px;
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto;
  color: #e98181;
  font-size: 1rem;
  font-weight: 700;
  background-color: #fff;
  border: 1px solid #e98181;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  .container .wrap-btn button.withdraw {
    width: 100%;
    height: 14vw;
    font-size: 4vw;
  }
}
.container .wrap-btn button.withdraw:hover {
  opacity: 0.8;
}

/* ---------------------------
 * *ダウンロードボタン
 * *----------------------------- */
.btnBsc-DL {
  background-color: #427fae;
}
.btnBsc-DL img {
  width: 24px;
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  .btnBsc-DL img {
    width: 18px;
  }
}

/* ---------------------------
 * *黒（濃グレー）ボタン
 * *----------------------------- */
.btnBsc-Black {
  background-color: #333;
}
.btnBsc-Black img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}
@media screen and (max-width: 767px) {
  .btnBsc-Black img {
    width: 13px;
    top: -1px;
  }
}

/* ---------------------------
 * *色ボタン（コーポレートカラー）
 * *----------------------------- */
.btnBsc-CoCor {
  background-color: #427fae;
}
.btnBsc-CoCor img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}
@media screen and (max-width: 767px) {
  .btnBsc-CoCor img {
    width: 13px;
    top: -1px;
  }
}

/* ---------------------------
 * *アクションボタン（会員登録など）
 * *----------------------------- */
.btn-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 40px;
  width: 100%;
  text-align: center;
  margin: 60px 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  .btn-form {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 7vw;
    margin: 10vw 0;
  }
}
.btn-form .btn.back {
  color: #427fae;
  font-size: 1rem;
  font-weight: 700;
  background-color: #fff;
  border: 1px solid #427fae;
}
@media screen and (max-width: 767px) {
  .btn-form .btn.back {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
}
@media screen and (max-width: 767px) {
  .btn-form .btn.entry {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
  }
}

/* ---------------------------
 * *リンク
 * *----------------------------- */
a.link-std {
  color: #427fae;
  text-decoration: underline;
}
a.link-std:hover {
  text-decoration: none;
  opacity: 1;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *お気に入りマーク
 * *********************************************************************** */
.com-item-box {
  display: inline-block;
  position: relative;
}
.com-item-box p.fav-mark {
  display: block;
  background: url("../img/common/icn_favorite_detail.svg") center 7px no-repeat;
  background-size: 22px 22px;
  width: 70px;
  margin: 0 0 0 1rem;
  padding: 23px 5px 2px;
  text-align: center;
  border: 1px solid #e9eaeb;
  border-radius: 4px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  .com-item-box p.fav-mark {
    background: url("../img/common/icn_favorite_detail.svg") center 1.8vw no-repeat;
    background-size: 5vw 5vw;
    width: 16vw;
    margin: 0 0 0 4vw;
    padding: 6.3vw 1vw 0.2vw;
    border-radius: 1vw;
  }
}
.com-item-box p.fav-mark span {
  color: #333;
  font-size: 10px;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  .com-item-box p.fav-mark span {
    font-size: 2.2vw;
  }
}
.com-item-box p.fav-mark.active {
  background-image: url("../img/common/icn_favorite_blue.svg");
  border: 1px solid #427fae;
}
.com-item-box p.fav-mark.active span {
  color: #427fae;
}
.com-item-box p.fav-mark:hover {
  background-image: url("../img/common/icn_favorite_blue.svg");
}

/* ログイン前は非表示 */
body.state-out span.fav-mark {
  display: none !important;
}
body.item_p-detail #terms.com-item-box {
  display: block;
}
body.item_p-detail #terms.com-item-box span.fav-mark {
  width: 40px;
  height: 40px;
  right: 20px;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *見出し
 * *********************************************************************** */
#main h2.page-ttl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  margin: 0;
  padding: 2.5rem 1rem 1.5rem;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl {
    margin: 0;
    padding: 6vw 4vw 2vw;
  }
}
#main h2.page-ttl .ttl {
  width: 100%;
  text-align: center;
  margin: 0.5rem 0;
  padding: 0;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: 2px;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl .ttl {
    margin: 0 0 0.5rem;
    font-size: 5.3vw;
    font-weight: 600;
  }
}
#main h2.page-ttl .ttl .red {
  color: #427fae;
}
#main h2.page-ttl .sub {
  margin: 0 0 0.2rem;
  font-size: 0.7rem;
  font-weight: 500;
  text-align: center;
  letter-spacing: 2px;
  font-family: "Noto Sans JP", sans-serif;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl .sub {
    margin: 0 0 2vw;
    font-size: 2.5vw;
  }
}
#main h2.page-ttl .ttl.specified {
  font-size: 1.2rem;
}
#main h2.page-ttl.list {
  height: 80px;
  margin: 40px 0 0;
}
@media screen and (max-width: 767px) {
  #main h2.page-ttl.list {
    height: 14vw;
    margin: 7vw 0 0;
  }
}
#main h3 {
  margin: 4rem 0 3rem;
  padding: 0;
  font-size: 2.2rem;
  font-weight: 600;
  line-height: 1.2;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main h3 {
    font-size: 5.5vw;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ページトップ
 * *********************************************************************** */
#page_top {
  position: fixed;
  display: block;
  width: 42px;
  height: 42px;
  right: 1rem;
  bottom: 1.2rem;
  background-color: rgba(9, 46, 86, 0.5);
  border-radius: 50%;
  z-index: 10;
}
#page_top a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  cursor: pointer;
  z-index: 11;
}
#page_top a:hover {
  opacity: 0.7;
}
#page_top a:before {
  content: "";
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
  position: absolute;
  top: calc(50% + 1px);
  left: calc(50% - 4px);
  margin-top: -4px;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *QA
 * *********************************************************************** */
#qa {
  padding: 60px 3rem;
}
@media screen and (max-width: 767px) {
  #qa {
    padding: 40px 1rem;
  }
}
#qa .qa-wrapper li {
  padding: 2.5rem 3rem 2.7rem;
  border-bottom: 1px solid #eee;
}
#qa .qa-wrapper li:first-child {
  border-top: 1px solid #eee;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper li {
    padding: 1.5rem 1rem 1.7rem;
  }
}
#qa .qa-wrapper li dl {
  font-size: 1.2rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper li dl {
    font-size: 1rem;
  }
}
#qa .qa-wrapper li dl dt {
  position: relative;
  margin: 0 0 1.5rem;
  padding: 0 0 0 4.5rem;
  color: #427fae;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper li dl dt {
    margin: 0 0 1rem;
    padding: 0 0 0 3rem;
  }
}
#qa .qa-wrapper li dl dt:before {
  position: absolute;
  top: 0;
  left: 0;
  content: "Q. ";
  font-size: 2rem;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper li dl dt:before {
    font-size: 1.6rem;
  }
}
#qa .qa-wrapper li dl dd {
  position: relative;
  padding: 0 0 0 4.5rem;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper li dl dd {
    padding: 0 0 0 3rem;
  }
}
#qa .qa-wrapper li dl dd:before {
  position: absolute;
  top: 0;
  left: 0.1rem;
  content: "A. ";
  font-size: 2rem;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper li dl dd:before {
    font-size: 1.6rem;
  }
}
#qa .qa-wrapper li dl dd picture {
  display: block;
  max-width: 1000px;
  margin: 1rem 0;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper li dl dd picture {
    max-width: 100%;
  }
}

/* ---------------------------
 * *アコーディオンの場合　#ac-menu
 * *----------------------------- */
#qa #ac-menu li {
  border-top: solid 1px #000;
}
#qa #ac-menu li:last-child {
  border-bottom: solid 1px #000;
}
#qa #ac-menu .label {
  cursor: pointer;
  font-size: 1.125rem;
  font-weight: bold;
  padding: 40px 30px;
  position: relative;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}
#qa #ac-menu .label:hover {
  background-color: #ffda5f;
}
#qa #ac-menu .label::before {
  content: "";
  width: 20px;
  height: 1px;
  background: #000;
  position: absolute;
  top: 50%;
  right: 5%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
#qa #ac-menu .label::after {
  content: "";
  width: 20px;
  height: 1px;
  background: #000;
  position: absolute;
  top: 50%;
  right: 5%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
  -webkit-transition: 0.5s;
  transition: 0.5s;
}
#qa #ac-menu .label.open {
  /* ラベルの背景色を変更 */
  background-color: #ffda5f;
}
#qa #ac-menu .label.open::before {
  /* ラベルアイコンの横棒を非表示 */
  opacity: 0;
}
#qa #ac-menu .label.open::after {
  /* ラベルアイコンの縦棒を横向きに回転 */
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
#qa #ac-menu .detail {
  border-top: solid 1px #ccc;
  padding: 35px 30px;
  display: none;
}
#qa #ac-menu .detail dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#qa #ac-menu .detail dt {
  width: 20%;
  font-weight: bold;
  margin-bottom: 40px;
}
#qa #ac-menu .detail dd {
  width: 80%;
  margin-bottom: 40px;
}

/*********************************************************************** */
#sample {
  padding: 100px 0;
}

/*------------------------------------------ */
#main .place-modal {
  position: relative;
  width: 100%;
  padding: 1rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .place-modal {
    padding: 1rem;
  }
}
#main .place-modal .btn.modal-open {
  width: 280px;
  max-width: 100%;
  height: 56px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #427fae;
  border-radius: 4px;
  /* --------------------------- */
}
#main .place-modal .modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 40px 20px;
  overflow: auto;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  z-index: 100;
}
#main .place-modal .modal-container:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
#main .place-modal .modal-container.active {
  opacity: 1;
  visibility: visible;
}
#main .place-modal .modal-container .modal-body {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  max-width: calc(100% - 2rem);
  width: 600px;
  margin: 0 auto;
}
#main .place-modal .modal-container .modal-body .modal-close {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  top: -30px;
  right: -30px;
  width: 60px;
  height: 60px;
  font-size: 22px;
  color: #fff;
  background-color: #427fae;
  border-radius: 30px;
  cursor: pointer;
  z-index: 120;
}
@media screen and (max-width: 767px) {
  #main .place-modal .modal-container .modal-body .modal-close {
    top: -36px;
  }
}
#main .place-modal .modal-container .modal-body .modal-content {
  position: relative;
  padding: 2rem;
  background-color: #fff;
  border-radius: 4px;
  z-index: 110;
}
@media screen and (max-width: 767px) {
  #main .place-modal .modal-container .modal-body .modal-content {
    padding: 2.5rem 1.5rem 1rem;
  }
}
#main .place-modal .modal-container .modal-body .modal-content .note {
  width: 100%;
  margin: 0 0 1rem;
  padding: 0;
  text-align: center;
}
#main .place-modal .modal-container .modal-body .modal-content .note span {
  display: inline-block;
  text-align: left;
}
#main .place-modal .modal-container .modal-body .modal-content .button-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#main .place-modal .modal-container .modal-body .modal-content .button-wrap button {
  width: calc(50% - 1rem);
  max-width: calc(100% - 1rem);
  height: 56px;
  margin: 1rem 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main .place-modal .modal-container .modal-body .modal-content .button-wrap button {
    width: calc((100% - 1rem) / 2);
    font-size: 0.9rem;
    line-height: 1.2;
  }
}
#main .place-modal .modal-container .modal-body .modal-content .button-wrap button.goto-signup {
  background-color: #427fae;
}
#main .place-modal .modal-container .modal-body .modal-content .button-wrap button.cancel {
  background-color: #ababab;
}
#main .place-modal .modal-container .modal-body .modal-content .button-wrap button + button {
  margin: 1rem 0 1rem 1rem;
}
/*# sourceMappingURL=parts.css.map */