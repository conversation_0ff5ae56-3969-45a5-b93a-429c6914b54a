@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *マイページ
 * *********************************************************************** */
/* Nav
 * *========================================== */
#main #mypage-head {
  padding: 0;
}
#main #mypage-head .container {
  width: 100%;
  padding: 0;
}
#main #mypage-head .container .nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 0;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
  border-bottom: 1px solid #e9eaeb;
}
#main #mypage-head .container .nav-wrap .nav-content {
  width: 20%;
  max-width: 100%;
  height: 80px;
  margin: 0;
  padding: 0;
  border-radius: 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content {
    width: 25%;
    height: 18vw;
    min-height: 12vw;
    margin: 0;
  }
}
#main #mypage-head .container .nav-wrap .nav-content:hover {
  opacity: 1;
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.bidding {
  background-image: url(../img/common/icn_mypage_nav_bid.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.account {
  background-image: url(../img/common/icn_mypage_nav_account.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.upload {
  margin-left: 3px;
  background-image: url(../img/common/icn_mypage_nav_upload.svg);
  background-size: 13px auto;
  background-position: center 2px;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content:hover span.upload {
    margin-left: 1px;
    background-size: 11px auto;
  }
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.card {
  background-image: url(../img/common/icn_mypage_nav_card.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover .label {
  color: #333;
}
#main #mypage-head .container .nav-wrap .nav-content a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  height: 100%;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 3.5vw 0 4vw;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span {
  display: inline-block;
  content: "";
  background-position: center;
  background-size: 18px auto;
  background-repeat: no-repeat;
  width: 18px;
  height: 18px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content a span {
    background-size: 16px auto;
    width: 16px;
    height: 16px;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a span {
    top: 0;
    left: calc(50% - 2.75vw);
    width: 5.5vw;
    height: 5.5vw;
    background-size: 5vw auto;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span.favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.bidding {
  background-image: url(../img/common/icn_mypage_nav_bid_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.account {
  background-image: url(../img/common/icn_mypage_nav_account_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.upload {
  margin-left: 3px;
  background-image: url(../img/common/icn_mypage_nav_upload_pgr.svg);
  background-size: 13px auto;
  background-position: center 2px;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content a span.upload {
    margin-left: 1px;
    background-size: 11px auto;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a span.upload {
    position: absolute;
    top: 3.6vw;
    left: 12vw;
    width: 3vw;
    height: 3.5vw;
    background-size: 2.8vw auto;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span.card {
  background-image: url(../img/common/icn_mypage_nav_card_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a .label {
  display: inline-block;
  padding: 0 0 0 5px;
  color: #c9c9c9;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.2;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media screen and (max-width: 1080px) {
  #main #mypage-head .container .nav-wrap .nav-content a .label {
    font-size: 0.8rem;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a .label {
    margin: auto 0 0;
    padding: 0;
    font-size: 2.2vw;
  }
}
#main #mypage-head .container .nav-wrap .nav-content.active {
  background-color: transparent;
  border-bottom: 3px solid #333;
}
#main #mypage-head .container .nav-wrap .nav-content.active a {
  position: relative;
  padding: 3px 0 0;
  cursor: default;
  pointer-events: none;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content.active a {
    padding: 3.5vw 0 calc(4vw - 3px);
  }
}
#main #mypage-head .container .nav-wrap .nav-content.active a:hover {
  opacity: 1;
}
#main #mypage-head .container .nav-wrap .nav-content.active a .favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .bidding {
  background-image: url(../img/common/icn_mypage_nav_bid.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .account {
  background-image: url(../img/common/icn_mypage_nav_account.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .upload {
  background-image: url(../img/common/icn_mypage_nav_upload.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .card {
  background-image: url(../img/common/icn_mypage_nav_card.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .label {
  color: #333;
  font-weight: 600;
}
#main #mypage-form {
  margin: 0 0 60px;
  padding: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-form {
    margin: 0 0 40px;
    padding: 0 0 1rem;
  }
}
/*# sourceMappingURL=mypage.css.map */