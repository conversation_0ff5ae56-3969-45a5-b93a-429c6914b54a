import {createI18n, useI18n} from 'vue-i18n'
import {translate, type TranslationKey} from './translate'

const i18n = createI18n<{message: Record<TranslationKey, string>}, 'ja' | 'en'>({
  legacy: false, // Vuetify does not support the legacy mode of vue-i18n
  warnHtmlMessage: false, // Disable warning on HTML messages
  locale: 'ja',
  fallbackLocale: 'en', // choose which language to use when your preferred language lacks a translation
  messages: translate,
})

// Export type-safe translation function
export const useTypedI18n = () => {
  const {t, locale, availableLocales} = useI18n()

  // Type-safe translation function
  const typedT = (key: TranslationKey): string => {
    return t(key as string) as string
  }

  return {
    t: typedT,
    locale,
    availableLocales,
  }
}

export default i18n
