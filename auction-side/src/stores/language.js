import {useStorage} from '@vueuse/core'
import {defineStore} from 'pinia'
import useApi from '../composables/useApi'
import {useAuthStore} from './auth'

/**
 * Get current language from local storage or browser
 * Set current language to local storage
 */
export const useLanguageStore = defineStore('language', {
  state: () => {
    const storedLanguage = useStorage('currentLanguage', null)
    const browserLang = navigator.language.split('-')[0]
    const supportedLangs = ['en', 'ja']
    const initialLang =
      storedLanguage.value || (supportedLangs.includes(browserLang) ? browserLang : 'ja')

    return {
      currentLanguage: useStorage('currentLanguage', initialLang),
      isLoading: false,
    }
  },
  actions: {
    setLanguage(lang) {
      this.currentLanguage = lang
    },
  },
  getters: {
    language: state => state.currentLanguage,
    loading: state => state.isLoading,
  },
})

/**
 * Change language in database when user is logged in
 * if user is not logged in, change language in local storage only
 */
export default function useChangeLanguage() {
  const auth = useAuthStore()
  const languageStore = useLanguageStore()
  const {apiExecute, parseHtmlResponseError} = useApi()

  const changeLanguage = async lang => {
    try {
      languageStore.setLanguage(lang)

      // Only call API if user is authenticated
      if (auth.isAuthenticated) {
        await apiExecute('private/change-language', {lang})
      }
    } catch (error) {
      const parsedError = parseHtmlResponseError(error)
      console.error('Error when changing language:', parsedError)
      throw error
    }
  }

  return {changeLanguage}
}
