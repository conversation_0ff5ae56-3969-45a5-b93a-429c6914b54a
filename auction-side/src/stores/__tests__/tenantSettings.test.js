import {createPinia, setActive<PERSON>inia} from 'pinia'
import {beforeEach, describe, expect, it, vi} from 'vitest'
import {useTenantSettingsStore} from '../tenantSettings'

// Mock the useApi composable
const mockApiExecute = vi.fn()
vi.mock('../../composables/useApi', () => ({
  default: () => ({
    apiExecute: mockApiExecute,
  }),
}))

// Mock the auth store
vi.mock('../auth', () => ({
  useAuthStore: () => ({
    isAuthenticated: false, // Test with unauthenticated user by default
  }),
}))

describe('Tenant Settings Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with correct default values', () => {
    const store = useTenantSettingsStore()

    expect(store.tenantSettings).toBe(null)
    expect(store.isLoading).toBe(false)
    expect(store.error).toBe(null)
    expect(store.isLoaded).toBe(false)
    expect(store.tenantName).toBe('')
    expect(store.companyName).toBe('')
  })

  it('should fetch tenant settings successfully', async () => {
    const mockResponse = {
      tenant_no: 1,
      tenant_name: 'Test Tenant',
      company_name: 'Test Company',
      function_options: {advanced_features: true},
      bid_options: {max_bid_amount: 500000},
      search_result_view_mode: 'panel',
    }

    mockApiExecute.mockResolvedValue(mockResponse)

    const store = useTenantSettingsStore()
    const result = await store.fetchTenantSettings()

    expect(mockApiExecute).toHaveBeenCalledWith('public/get-tenant-settings', {})
    expect(store.tenantSettings).toEqual(mockResponse)
    expect(store.isLoading).toBe(false)
    expect(store.isLoaded).toBe(true)
    expect(store.tenantName).toBe('Test Tenant')
    expect(store.companyName).toBe('Test Company')
    expect(result).toEqual(mockResponse)
  })

  it('should fetch tenant settings for unauthenticated users', async () => {
    const mockResponse = {
      tenant_no: 1,
      tenant_name: 'Public Tenant',
      company_name: 'Public Company',
      function_options: {},
      bid_options: {},
      search_result_view_mode: 'panel',
    }

    mockApiExecute.mockResolvedValue(mockResponse)

    const store = useTenantSettingsStore()
    // Should work even when user is not authenticated
    const result = await store.fetchTenantSettings()

    expect(mockApiExecute).toHaveBeenCalledWith('public/get-tenant-settings', {})
    expect(store.tenantSettings).toEqual(mockResponse)
    expect(store.tenantName).toBe('Public Tenant')
    expect(store.companyName).toBe('Public Company')
    expect(result).toEqual(mockResponse)
  })

  it('should handle API errors gracefully', async () => {
    const mockError = new Error('API Error')
    mockApiExecute.mockRejectedValue(mockError)

    const store = useTenantSettingsStore()

    await expect(store.fetchTenantSettings()).rejects.toThrow('API Error')
    expect(store.error).toBe(mockError)
    expect(store.isLoading).toBe(false)
    expect(store.tenantSettings).toBe(null)
  })

  it('should check feature flags correctly', () => {
    const store = useTenantSettingsStore()

    // Set mock data
    store.tenantSettings = {
      function_options: {
        advanced_features: true,
        premium_support: false,
      },
    }

    expect(store.isFeatureEnabled('advanced_features')).toBe(true)
    expect(store.isFeatureEnabled('premium_support')).toBe(false)
    expect(store.isFeatureEnabled('non_existent')).toBe(false)
  })

  it('should get bid options with defaults', () => {
    const store = useTenantSettingsStore()

    // Set mock data
    store.tenantSettings = {
      bid_options: {
        max_bid_amount: 500000,
        min_increment: 1000,
      },
    }

    expect(store.getBidOption('max_bid_amount')).toBe(500000)
    expect(store.getBidOption('min_increment', 500)).toBe(1000)
    expect(store.getBidOption('non_existent', 'default')).toBe('default')
  })

  it('should clear tenant settings', () => {
    const store = useTenantSettingsStore()

    // Set some data first
    store.tenantSettings = {tenant_name: 'Test'}
    store.error = new Error('Test error')
    store.lastFetched = new Date()

    store.clearTenantSettings()

    expect(store.tenantSettings).toBe(null)
    expect(store.error).toBe(null)
    expect(store.lastFetched).toBe(null)
  })
})
