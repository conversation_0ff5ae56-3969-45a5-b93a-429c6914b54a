/**
 * Simplified test suite for language store functionality
 */

import {createPinia, setActivePinia} from 'pinia'
import {beforeEach, describe, expect, it, vi} from 'vitest'

// Mock dependencies first
const mockUseStorage = vi.fn((key, defaultValue) => ({
  value: defaultValue === null ? 'ja' : defaultValue,
}))

const mockApiExecute = vi.fn()
const mockParseError = vi.fn()

vi.mock('@vueuse/core', () => ({
  useStorage: mockUseStorage,
}))

vi.mock('@/composables/useApi', () => ({
  default: () => ({
    apiExecute: mockApiExecute,
    parseHtmlResponseError: mockParseError,
  }),
}))

vi.mock('@/stores/auth', () => ({
  useAuthStore: () => ({
    isAuthenticated: false,
  }),
}))

// Import after mocking
import useChangeLanguage, {useLanguageStore} from '@/stores/language'

describe('Language Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('useLanguageStore', () => {
    it('initializes with correct default state', () => {
      const store = useLanguageStore()

      expect(store.language).toBe('ja')
      expect(store.loading).toBe(false)
    })

    it('can set language', () => {
      const store = useLanguageStore()

      store.setLanguage('en')

      expect(store.language).toBe('en')
    })

    it('can set loading state', () => {
      const store = useLanguageStore()

      expect(store.loading).toBe(true)
    })
  })

  describe('useChangeLanguage', () => {
    it('returns changeLanguage function', () => {
      const {changeLanguage} = useChangeLanguage()

      expect(typeof changeLanguage).toBe('function')
    })

    it('calls store setLanguage when changing language', async () => {
      const store = useLanguageStore()
      const {changeLanguage} = useChangeLanguage()

      await changeLanguage('en')

      expect(store.language).toBe('en')
    })
  })
})
