<!-- <script setup>
  import {computed, defineAsyncComponent, onMounted, watch} from 'vue'
  import {onBeforeRouteLeave, useRoute} from 'vue-router'
  import {CLASSIFICATIONS, PATH_NAME} from '../../defined/const'

  import {useLocale} from 'vuetify'
  import ClassificationSwitch from '../../components/common/ClassificationSwitch.vue'
  import HeaderMenu from '../../components/mypage/HeaderMenu.vue'
  import useSearchProducts from '../../composables/searchProducts'
  import {useSearchResultStore} from '../../stores/search-results'

  const ProductList = defineAsyncComponent(() => import(/* WebpackChunkName: "ProductList" */ '../../components/search-list/ProductList.vue'))
  const FilterBox = defineAsyncComponent(() => import(/* webpackChunkName: "FilterBox" */ '../../components/search-list/parts/FilterBox.vue'))

  const route = useRoute()
  const searchResultStore = useSearchResultStore()
  const {searchSuccessfulBidHistory, resetSearchFilters, getConstants} = useSearchProducts()
  const {t} = useLocale()

  const productList = computed(() => searchResultStore.productList.all)

  // 各画面取得
  // デフォルトで競り上がり式検索
  const handleSearch = (classification = CLASSIFICATIONS.ASCENDING) => {
    resetSearchFilters()
    searchResultStore.selectedAucClassification = classification
    const path = route.path
    if (path === PATH_NAME.BID_HISTORY) {
      searchSuccessfulBidHistory({auctionClassification: [classification === CLASSIFICATIONS.SEALED ? 2 : 1]})
    }
  }

  // パスが異なる場合のみ、再レンダリングする。
  watch(
    () => route.path,
    (newPath, oldPath) => {
      if (newPath !== oldPath) {
        handleSearch()
      }
    }
  )

  // 画面表示時に検索処理を実行
  onMounted(async () => {
    await Promise.all([getConstants(), handleSearch()])
  })

  onBeforeRouteLeave(() => {
    searchResultStore.viewMore = 1
  })
</script>
<template>
  <main id="main">
    <div class="ttl-mypage">
      <p class="ttl">{{ t('user.myPage') }}</p>
    </div>
    <HeaderMenu />
    <ClassificationSwitch @click:changeClassification="handleSearch" />

    <h1 class="mb0">
      {{ t(route.meta.label) }}
      <span
        ><span class="type sealed">{{
          searchResultStore.selectedAucClassification === CLASSIFICATIONS.ASCENDING ? t('CLASSIFICATION_ASCENDING') : t('CLASSIFICATION_SEALED')
        }}</span></span
      >
    </h1>

    <FilterBox @clicked:search="handleSearch" :classification="searchResultStore.selectedAucClassification" />
    <section id="list-auction">
      <div class="container">
        <ProductList v-if="productList && productList.length > 0" :productList="productList" />
      </div>
    </section>
  </main>
</template> -->
