import {computed, ref} from 'vue'
import {useLocale} from 'vuetify'
import {CLASSIFICATIONS} from '../defined/const'
import {useMessageDialogStore} from '../stores/messag-dialog'
import {useSearchResultStore} from '../stores/search-results'
import {formatDateString, priceLocaleString} from './common'
import useApi from './useApi'

/**
 * Search products
 */
export default function useSearchProducts() {
  const {apiExecute, parseHtmlResponseError} = useApi()
  const {t} = useLocale()
  const store = useSearchResultStore()
  const dialog = useMessageDialogStore()

  const today = new Date()
  const lastYear = new Date()
  lastYear.setFullYear(today.getFullYear() - 1)

  const loading = ref(false)
  const count = ref(0)

  const constants = computed(() => store.constants)
  const storedSearchKey = computed(() => store.searchKey)

  const setProductListFromResponse = searchResult => {
    const itemList = searchResult?.items ?? []
    count.value = itemList.length ?? 0
    // searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map(item => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(item.end_datetime)
      const {datePart: startDatePart, timePart: startTimePart} = formatDateString(
        item.start_datetime
      )

      // Current item in store
      const currentItem = store.productList.all.find(
        x => x.exhibition_item_no === item.exhibition_item_no
      )

      return {
        ...item,
        category: item.category_id,
        itemNo: item.item_no,
        link: `/details/${item.exhibition_item_no}`,
        productName: item.free_field.productName,
        currentPrice: item.bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          item.bid_status.current_price +
            item.bid_status.current_price * (item.bid_status.tax_rate / 100)
        )?.toLocaleString(),
        noOfBids: item.attention_info.bid_count,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
        bidPrice: currentItem?.bidPrice || priceLocaleString(item.bid_status.bid_price), // input bid price value on screen will be saved here
        bidQuantity: currentItem?.bidQuantity || priceLocaleString(item.bid_status.bid_quantity), // input bid quantity
        bidInputError: currentItem?.bidInputError || {
          bidPrice: null,
          bidQuantity: null,
        }, // error message for bid price and quantity
      }
    })
    // Update product list in store
    const newList = store.productList.all.filter(
      x => x.exhibition_no !== formattedItems[0].exhibition_no
    )
    store.productList.all = [...newList, ...formattedItems]
  }

  // search and sort products then save to store
  const search = async inputParams => {
    loading.value = true
    const {
      category = null,
      exhibitionNos = [],
      searchKey = storedSearchKey.value,
      unSoldOut = store.unSoldOut,
      favorite = store.favorite,
      bidding = false,
      initLimit = store.showCount * store.viewMore,
      modelList = store.modelList,
      categoryList = store.categoryList,
      exhibitionItemNos = null,
      startPrice = store.startPrice ? Number(store.startPrice) : null,
      endPrice = store.endPrice ? Number(store.endPrice) : null,
      sorter = store.sorter,
      brandList = store.brandList,
      auction_classification = null,
    } = inputParams

    // set favorite flag
    store.favorite = favorite

    // categoryList をチェックするための一時変数
    const tempCategoryList = Array.isArray(categoryList) ? categoryList : []

    // // 一時変数のチェック、categoryList = [null] の場合は空配列にする
    // if (tempCategoryList.length === 1 && tempCategoryList[0] === null) {
    //   tempCategoryList = []
    // } else if (inputParams.favorite || inputParams.bidding) {
    //   tempCategoryList = []
    // }

    // brandList をチェックするための一時変数
    let tempBrandList = Array.isArray(brandList) ? brandList : []

    // 一時変数のチェック、brandList = [null] の場合は空配列にする
    if (tempBrandList.length === 1 && tempBrandList[0] === null) {
      tempBrandList = []
    }

    const params = {
      category,
      searchKey,
      unSoldOut,
      favorite,
      bidding,
      auction_classification,
      exhibitionNos,
      initLimit,
      limit: initLimit,
      showedItemNos: null,
      startPrice,
      endPrice,
      sorter,
      timeover: null,
      modelList,
      categoryList: tempCategoryList,
      exhibitionItemNos,
      brandList: tempBrandList,
    }
    await apiExecute('public/search-auction-items', params)
      .then(response => {
        store.setProductList(response)
      })
      .catch(error => parseHtmlResponseError(error))
    loading.value = false
  }

  // search and sort products then update current exhibition only. use in "LoadMore"
  const searchScope = async (
    inputParams = {
      category: null,
      exhibitionNos: [],
      searchKey: storedSearchKey.value,
      unSoldOut: store.unSoldOut,
      favorite: store.favorite,
      bidding: false,
      initLimit: store.showCount * store.viewMore,
      categoryList: store.categoryList,
      exhibitionItemNos: null,
      auction_classification: null,
    }
  ) => {
    loading.value = true
    const {
      category = null,
      exhibitionNos = [],
      searchKey = storedSearchKey.value,
      unSoldOut = store.unSoldOut,
      favorite = store.favorite,
      bidding = false,
      initLimit = store.showCount * store.viewMore,
      categoryList = store.categoryList,
      exhibitionItemNos = null,
      startPrice = store.startPrice ? Number(store.startPrice) : null,
      endPrice = store.endPrice ? Number(store.endPrice) : null,
      sorter = store.sorter,
      auction_classification,
    } = inputParams

    // set favorite flag
    store.favorite = favorite

    // categoryList をチェックするための一時変数
    let tempCategoryList = Array.isArray(categoryList) ? categoryList : []

    // 一時変数のチェック、categoryList = [null] の場合は空配列にする
    if (tempCategoryList.length === 1 && tempCategoryList[0] === null) {
      tempCategoryList = []
    } else if (inputParams.favorite || inputParams.bidding) {
      tempCategoryList = []
    }

    const params = {
      category,
      searchKey,
      unSoldOut,
      favorite,
      bidding,
      auction_classification,
      exhibitionNos,
      initLimit,
      limit: initLimit,
      showedItemNos: null,
      startPrice,
      endPrice,
      sorter,
      timeover: null,
      categoryList: tempCategoryList,
      exhibitionItemNos,
    }

    await apiExecute('public/search-auction-items', params)
      .then(response => {
        setProductListFromResponse(response)
      })
      .catch(error => parseHtmlResponseError(error))
    loading.value = false
  }

  const searchSuccessfulBidHistory = async (inputParams = {}) => {
    loading.value = true
    const classification = store.selectedAucClassification === CLASSIFICATIONS.ASCENDING ? 1 : 2
    const {
      auctionClassification = [classification],
      startDatetime = lastYear.toISOString().split('T')[0],
      endDatetime = today.toISOString().split('T')[0],
      initLimit = store.showCount * store.viewMore,
      searchKey = storedSearchKey.value,
      categoryList = store.categoryList,
    } = inputParams

    try {
      const params = {
        auction_classification: auctionClassification,
        start_datetime: startDatetime,
        end_datetime: endDatetime,
        searchKey,
        categoryList,
        limit: initLimit,
      }

      const response = await apiExecute('private/get-successful-bid-history', params)

      store.setBidHistory(response)
    } catch (error) {
      const err = parseHtmlResponseError(error)
      console.log({err})
      dialog.setShowMessage(err.message ?? t('common.error'), {isErr: true})
    }
    loading.value = false
  }

  const searchAllSuccessfulBidHistory = async (inputParams = {}) => {
    loading.value = true
    const {
      auctionClassification = [],
      startDatetime = lastYear.toISOString().split('T')[0],
      endDatetime = today.toISOString().split('T')[0],
      initLimit = store.showCount * store.viewMore,
      searchKey = storedSearchKey.value,
      categoryList = store.categoryList,
    } = inputParams

    try {
      const params = {
        auction_classification: auctionClassification,
        start_datetime: startDatetime,
        end_datetime: endDatetime,
        searchKey,
        categoryList,
        limit: initLimit,
      }

      const response = await apiExecute('public/get-all-successful-bid-history', params)

      store.setBidHistory(response)
    } catch (error) {
      const err = parseHtmlResponseError(error)
      console.log({err})
      dialog.setShowMessage(err.message ?? t('common.error'), {isErr: true})
    }
    loading.value = false
  }

  const searchWithModels = async (category, {checkedCategories = []}) => {
    if (Array.isArray(store.categoryList)) {
      store.categoryList.splice(0, store.categoryList.length)
    }
    checkedCategories.map(model => store.categoryList.push(model))
    const reqParams = {
      category: category ?? null,
      unSoldOut: store.unSoldOut,
      searchKey: storedSearchKey.value,
      categoryList: checkedCategories,
    }
    await search(reqParams)
  }

  const getConstants = async () => {
    loading.value = true
    const params = {}
    await apiExecute('public/get-item-search-constants', params).then(response => {
      store.setSearchConstants(response)
      return Promise.resolve()
    })
    loading.value = false
  }

  const countUpViewMore = () => {
    store.viewMore++
  }

  const resetSearchFilters = () => {
    store.resetProductList()
    store.resetSearchFilters()
  }

  return {
    loading,
    constants,
    count,
    search,
    searchScope,
    searchWithModels,
    searchSuccessfulBidHistory,
    searchAllSuccessfulBidHistory,
    getConstants,
    countUpViewMore,
    resetSearchFilters,
  }
}
