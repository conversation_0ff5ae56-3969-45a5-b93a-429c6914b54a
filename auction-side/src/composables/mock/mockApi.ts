// Mock data for development

/**
 * Generate randomized auction items for top page sections
 * @param {string} type - 'new' or 'recommended'
 * @returns {Object} Mock API response with generated items
 */
const generateTopPageItems = (type: 'new' | 'recommended') => {
  const productNames = [
    '【正規品】シャネル CHANEL ミニマトラッセ キャビアスキン チェーンバッグ',
    'ルイヴィトン ダミエ アズール ネヴァーフル GM トートバッグ 正規品',
    'エルメス バーキン30 トゴ ブラック シルバー金具 正規品 ハンドバッグ',
    'ロレックス デイトナ 116500LN ホワイト文字盤 正規品 メンズ腕時計',
    'グッチ GGマーモント キルティング ショルダーバッグ レザー',
    'プラダ サフィアーノ トートバッグ 2WAY ハンドバッグ ネイビー',
    'フェンディ ピーカブー レギュラー ハンドバッグ レザー',
    'セリーヌ ラゲージ マイクロ ハンドバッグ カーフレザー',
    'ディオール レディディオール ハンドバッグ カナージュ',
    'ボッテガヴェネタ イントレチャート トートバッグ レザー',
    'バレンシアガ シティ ハンドバッグ レザー クラシック',
    'サンローラン モノグラム ショルダーバッグ キルティング',
    'ジバンシー アンティゴナ ハンドバッグ レザー',
    'ロエベ ハンモック バッグ スモール カーフレザー',
    'マルニ トランク バッグ ミニ レザー ハンドバッグ',
  ]

  const conditions = ['新品', '未使用', '未使用に近い', '中古A', '中古B']
  const brands = ['CHANEL', 'LOUIS VUITTON', 'HERMES', 'GUCCI', 'PRADA', 'FENDI', 'CELINE', 'DIOR']
  const colors = ['ブラック', 'ホワイト', 'ネイビー', 'ブラウン', 'ベージュ', 'レッド', 'ピンク']

  const items = []

  for (let i = 0; i < 10; i++) {
    const isAscending = Math.random() > 0.5
    const auctionClassification = isAscending ? 1 : 2
    const isAdItem = type === 'recommended' ? true : Math.random() > 0.3 // More ad items for recommended

    // Generate random prices
    const startPrice = Math.floor(Math.random() * 500000) + 10000 // 10,000 - 510,000
    const currentPrice = startPrice + Math.floor(Math.random() * 200000) // Add 0-200,000
    const instantPrice = currentPrice + Math.floor(Math.random() * 300000) + 50000 // Add 50,000-350,000

    // Generate random time remaining (in milliseconds)
    const hoursRemaining = Math.floor(Math.random() * 72) + 1 // 1-72 hours
    const endDateTime = new Date(Date.now() + hoursRemaining * 3600000).toISOString()

    const item = {
      exhibition_item_no: `${type.toUpperCase()}_${String(i + 1).padStart(3, '0')}`,
      item_no: `ITEM_${type.toUpperCase()}_${String(i + 1).padStart(3, '0')}`,
      category_id: Math.floor(Math.random() * 5) + 1,
      auction_classification: auctionClassification,
      isAdItem: isAdItem,
      free_field: {
        productName: `${type === 'new' ? '【新着】' : '【おすすめ】'}${productNames[Math.floor(Math.random() * productNames.length)]} ${colors[Math.floor(Math.random() * colors.length)]}`,
        image_url: `/assets/img/item/top_item0${(i % 6) + 1}.png`,
        condition: conditions[Math.floor(Math.random() * conditions.length)],
        shipping_free: Math.random() > 0.4, // 60% chance of free shipping
        instant_price: instantPrice,
        minimum_bid_price: startPrice,
        start_price: startPrice,
        brand: brands[Math.floor(Math.random() * brands.length)],
      },
      bid_status: {
        current_price: currentPrice,
        bid_price: isAdItem ? 0 : currentPrice + Math.floor(Math.random() * 10000),
        bid_quantity: 1,
        tax_rate: 10,
        is_top_member: Math.random() > 0.7, // 30% chance of being top member
        minimum_bid_exceeded: currentPrice > startPrice,
      },
      attention_info: {
        bid_count: Math.floor(Math.random() * 200) + 1,
        favorited_count: Math.floor(Math.random() * 50) + 1,
        view_count: Math.floor(Math.random() * 1000) + 100,
        is_favorited: Math.random() > 0.8, // 20% chance of being favorited
      },
      start_datetime: new Date(Date.now() - Math.floor(Math.random() * *********)).toISOString(), // Started within last week
      end_datetime: endDateTime,
      exhibition_no: `EXH_${type.toUpperCase()}_${String(i + 1).padStart(3, '0')}`,
      status: 'active',
    }

    items.push(item)
  }

  return {
    items: items,
    count: 10,
    total_count: 10,
    current_count: 10,
    status: 'success',
  }
}

export const getMockData = (path: string): any => {
  switch (path) {
    case 'public/get-item-search-constants':
      return [
        {key_string: 'PRODUCT_CATEGORY', value1: '1', value2: 'カテゴリ1'},
        {key_string: 'PRODUCT_CATEGORY', value1: '2', value2: 'カテゴリ2'},
        {key_string: 'TAX_RATE', value1: '10', value2: '10%'},
        {key_string: 'PITCH_FOLLOW_BID_PRICE', value1: '100', value2: '100円'},
      ]
    case 'public/search-auction-items':
      return {
        items: [
          {
            exhibition_item_no: 'MOCK001',
            item_no: 'ITEM001',
            category_id: 1,
            auction_classification: 1, // 1: ascending, 2: sealed
            isAdItem: false, // Regular auction item - biddable
            free_field: {
              productName:
                '【正規品】1円スタート レア シャネル CHANEL ミニマトラッセ マトラッセ キャビアスキン チェーンバッグ トップハンドル 黒',
              image_url: '/assets/img/item/top_item01.png',
              condition: '未使用',
              shipping_free: true,
              instant_price: 890000,
              minimum_bid_price: 1,
              start_price: 1,
            },
            bid_status: {
              current_price: 380000,
              bid_price: 380000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 884,
              favorited_count: 34,
              view_count: 1200,
              is_favorited: false,
            },
            start_datetime: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            end_datetime: new Date(Date.now() + 7200000).toISOString(), // 2 hours from now
            exhibition_no: 'EXH001',
            status: 'active',
          },
          {
            exhibition_item_no: 'MOCK002',
            item_no: 'ITEM002',
            category_id: 2,
            auction_classification: 1, // ascending
            isAdItem: true, // Advertisement item - display only
            free_field: {
              productName:
                'ルイヴィトン ダミエ アズール ネヴァーフル GM トートバッグ 正規品 アイボリー ホワイト LOUIS VUITTON ショルダー バッグ',
              image_url: '/assets/img/item/top_item02.png',
              condition: '中古A',
              shipping_free: false,
              instant_price: 450000,
              minimum_bid_price: 5000,
              start_price: 5000,
            },
            bid_status: {
              current_price: 250000,
              bid_price: 260000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: false,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 156,
              favorited_count: 12,
              view_count: 800,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 2 days ago
            end_datetime: new Date(Date.now() + 86400000).toISOString(), // 1 day from now
            exhibition_no: 'EXH002',
            status: 'active',
          },
          {
            exhibition_item_no: 'MOCK003',
            item_no: 'ITEM003',
            category_id: 3,
            auction_classification: 2, // sealed
            isAdItem: false, // Regular auction item - biddable
            free_field: {
              productName: 'エルメス バーキン30 トゴ ブラック シルバー金具 正規品 ハンドバッグ',
              image_url: '/assets/img/item/top_item03.png',
              condition: '中古A',
              shipping_free: true,
              instant_price: 1800000,
              minimum_bid_price: 800000,
              start_price: 800000,
            },
            bid_status: {
              current_price: 1650000,
              bid_price: 1700000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 45,
              favorited_count: 89,
              view_count: 2100,
              is_favorited: false,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 3 days ago
            end_datetime: new Date(Date.now() + 1800000).toISOString(), // 30 minutes from now
            exhibition_no: 'EXH003',
            status: 'active',
          },
          {
            exhibition_item_no: 'MOCK004',
            item_no: 'ITEM004',
            category_id: 4,
            auction_classification: 1, // ascending
            isAdItem: true, // Advertisement item - display only
            free_field: {
              productName: 'ロレックス デイトナ 116500LN ホワイト文字盤 正規品 メンズ腕時計',
              image_url: '/assets/img/item/top_item04.png',
              condition: '新品',
              shipping_free: true,
              instant_price: 2500000,
              minimum_bid_price: 1500000,
              start_price: 1500000,
            },
            bid_status: {
              current_price: 1500000,
              bid_price: 0,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: false,
              minimum_bid_exceeded: false,
            },
            attention_info: {
              bid_count: 0,
              favorited_count: 67,
              view_count: 450,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
            end_datetime: new Date(Date.now() + *********).toISOString(), // 2 days from now
            exhibition_no: 'EXH004',
            status: 'active',
          },
          {
            exhibition_item_no: 'MOCK005',
            item_no: 'ITEM005',
            category_id: 1,
            auction_classification: 2, // sealed
            isAdItem: false, // Regular auction item - biddable
            free_field: {
              productName: 'グッチ GGマーモント キルティング ショルダーバッグ レザー ブラック',
              image_url: '/assets/img/item/top_item05.png',
              condition: '中古B',
              shipping_free: false,
              instant_price: 180000,
              minimum_bid_price: 50000,
              start_price: 50000,
            },
            bid_status: {
              current_price: 125000,
              bid_price: 130000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: false,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 23,
              favorited_count: 8,
              view_count: 320,
              is_favorited: false,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 1 week ago
            end_datetime: new Date(Date.now() + 43200000).toISOString(), // 12 hours from now
            exhibition_no: 'EXH005',
            status: 'active',
          },
          {
            exhibition_item_no: 'MOCK006',
            item_no: 'ITEM006',
            category_id: 2,
            auction_classification: 1, // ascending
            isAdItem: true, // Advertisement item - display only
            free_field: {
              productName: 'プラダ サフィアーノ トートバッグ 2WAY ハンドバッグ ネイビー 正規品',
              image_url: '/assets/img/item/top_item06.png',
              condition: '中古A',
              shipping_free: true,
              instant_price: 320000,
              minimum_bid_price: 100000,
              start_price: 100000,
            },
            bid_status: {
              current_price: 280000,
              bid_price: 0,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: false,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 78,
              favorited_count: 15,
              view_count: 890,
              is_favorited: false,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 4 days ago
            end_datetime: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago (ended)
            exhibition_no: 'EXH006',
            status: 'soldout',
          },
        ],
        count: 156,
        total_count: 156,
        current_count: 6,
        status: 'success',
      }
    case 'public/get-new-notices':
      return {
        notices: [
          {
            notice_no: 1,
            title: 'モック通知',
            sub_title: 'テスト用通知',
            body: 'これはモック環境での通知です。',
            create_date: new Date().toISOString(),
            display_code: 1,
            file: [],
            link_url: '',
            title1: 'モック通知',
          },
        ],
        total_count: 1,
        status: 'success',
      }
    case 'private/get-change-info-constants':
      return {
        constants: [
          {key: 'country', value1: 'JP', value2: '日本'},
          {key: 'country', value1: 'US', value2: 'アメリカ'},
        ],
        member: {
          nickname: 'モックユーザー',
          email: '<EMAIL>',
          country: 'JP',
          companyName: 'モック会社',
          tel: '03-1234-5678',
        },
        status: 'success',
      }
    case 'private/change-member-info':
      return {
        status: 'success',
        message: 'Member information updated successfully',
      }
    case 'private/change-member-password':
      return {
        status: 'success',
        message: 'Password changed successfully',
      }
    case 'private/withdraw-member':
      return {
        status: 'success',
        message: 'Member withdrawal completed',
      }
    case '/get-auction-common-constants':
    case 'get-auction-common-constants':
      return [
        {key: 'CURRENCY', value1: 'JPY', value2: '円'},
        {key: 'TIMEZONE', value1: 'Asia/Tokyo', value2: 'JST'},
        {key: 'LANGUAGE', value1: 'ja', value2: '日本語'},
      ]
    case 'get-member-regist-constants':
      return [
        {key_string: 'COUNTRY_CODE', value1: 'JP', value2: '日本'},
        {key_string: 'COUNTRY_CODE', value1: 'US', value2: 'アメリカ'},
        {key_string: 'COUNTRY_CODE', value1: 'CN', value2: '中国'},
      ]
    case 'request-member':
      return {
        status: 'success',
        message: 'Member registration completed successfully',
      }
    case 'private/get-bidding-items':
      return {
        items: [
          {
            exhibition_item_no: 'BIDDING001',
            item_no: 'ITEM_BIDDING001',
            category_id: 1,
            auction_classification: 1, // ascending
            isAdItem: false,
            free_field: {
              productName:
                '【入札中】シャネル マトラッセ チェーンショルダーバッグ ラムスキン ブラック',
              image_url: '/assets/img/item/top_item01.png',
              condition: '中古A',
              shipping_free: true,
              instant_price: 650000,
              minimum_bid_price: 100000,
              start_price: 100000,
            },
            bid_status: {
              current_price: 420000,
              bid_price: 430000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 67,
              favorited_count: 23,
              view_count: 890,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 2 days ago
            end_datetime: new Date(Date.now() + 3600000).toISOString(), // 1 hour from now
            exhibition_no: 'EXH_BIDDING001',
            status: 'active',
          },
          {
            exhibition_item_no: 'BIDDING002',
            item_no: 'ITEM_BIDDING002',
            category_id: 2,
            auction_classification: 2, // sealed
            isAdItem: false,
            free_field: {
              productName: '【入札中】エルメス ケリー28 エプソン ローズサクラ シルバー金具',
              image_url: '/assets/img/item/top_item02.png',
              condition: '新品同様',
              shipping_free: true,
              instant_price: 2200000,
              minimum_bid_price: 1200000,
              start_price: 1200000,
            },
            bid_status: {
              current_price: 1800000,
              bid_price: 1850000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: false,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 12,
              favorited_count: 45,
              view_count: 1200,
              is_favorited: false,
            },
            start_datetime: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            end_datetime: new Date(Date.now() + 7200000).toISOString(), // 2 hours from now
            exhibition_no: 'EXH_BIDDING002',
            status: 'active',
          },
          {
            exhibition_item_no: 'BIDDING003',
            item_no: 'ITEM_BIDDING003',
            category_id: 3,
            auction_classification: 1, // ascending
            isAdItem: false,
            free_field: {
              productName: '【入札中】ロレックス サブマリーナ デイト 116610LN ブラック文字盤',
              image_url: '/assets/img/item/top_item03.png',
              condition: '中古A',
              shipping_free: false,
              instant_price: 1800000,
              minimum_bid_price: 800000,
              start_price: 800000,
            },
            bid_status: {
              current_price: 1200000,
              bid_price: 1250000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
            },
            attention_info: {
              bid_count: 34,
              favorited_count: 78,
              view_count: 1500,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 3 days ago
            end_datetime: new Date(Date.now() + 14400000).toISOString(), // 4 hours from now
            exhibition_no: 'EXH_BIDDING003',
            status: 'active',
          },
        ],
        count: 3,
        total_count: 3,
        current_count: 3,
        status: 'success',
      }
    case 'private/get-successful-bid-history':
      return {
        items: [
          {
            exhibition_item_no: 'HISTORY001',
            item_no: 'ITEM_HISTORY001',
            category_id: 1,
            auction_classification: 1, // ascending
            isAdItem: false,
            free_field: {
              productName: '【落札済】ルイヴィトン ダミエ アズール ネヴァーフル GM トートバッグ',
              image_url: '/assets/img/item/top_item01.png',
              condition: '未使用に近い',
              shipping_free: true,
              instant_price: 200000,
              minimum_bid_price: 100000,
              start_price: 100000,
            },
            bid_status: {
              current_price: 235400,
              bid_price: 235400,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
              winning_price: 235400,
            },
            attention_info: {
              bid_count: 884,
              favorited_count: 34,
              view_count: 1200,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - *********).toISOString(), // 1 week ago
            end_datetime: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            exhibition_no: 'EXH_HISTORY001',
            status: 'soldout',
          },
          {
            exhibition_item_no: 'HISTORY002',
            item_no: 'ITEM_HISTORY002',
            category_id: 2,
            auction_classification: 2, // sealed
            isAdItem: false,
            free_field: {
              productName: '【落札済】グッチ GGマーモント キルティング ショルダーバッグ',
              image_url: '/assets/img/item/top_item02.png',
              condition: '中古B',
              shipping_free: false,
              instant_price: 180000,
              minimum_bid_price: 50000,
              start_price: 50000,
            },
            bid_status: {
              current_price: 145000,
              bid_price: 145000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: false,
              minimum_bid_exceeded: true,
              winning_price: 145000,
            },
            attention_info: {
              bid_count: 23,
              favorited_count: 8,
              view_count: 320,
              is_favorited: false,
            },
            start_datetime: new Date(Date.now() - 1209600000).toISOString(), // 2 weeks ago
            end_datetime: new Date(Date.now() - *********).toISOString(), // 3 days ago
            exhibition_no: 'EXH_HISTORY002',
            status: 'soldout',
          },
          {
            exhibition_item_no: 'HISTORY003',
            item_no: 'ITEM_HISTORY003',
            category_id: 3,
            auction_classification: 1, // ascending
            isAdItem: false,
            free_field: {
              productName: '【落札済】プラダ サフィアーノ トートバッグ 2WAY ハンドバッグ',
              image_url: '/assets/img/item/top_item03.png',
              condition: '中古A',
              shipping_free: true,
              instant_price: 320000,
              minimum_bid_price: 100000,
              start_price: 100000,
            },
            bid_status: {
              current_price: 298000,
              bid_price: 298000,
              bid_quantity: 1,
              tax_rate: 10,
              is_top_member: true,
              minimum_bid_exceeded: true,
              winning_price: 298000,
            },
            attention_info: {
              bid_count: 78,
              favorited_count: 15,
              view_count: 890,
              is_favorited: true,
            },
            start_datetime: new Date(Date.now() - 1814400000).toISOString(), // 3 weeks ago
            end_datetime: new Date(Date.now() - *********).toISOString(), // 1 week ago
            exhibition_no: 'EXH_HISTORY003',
            status: 'soldout',
          },
        ],
        count: 3,
        total_count: 3,
        current_count: 3,
        status: 'success',
      }
    case 'public/get-new-auction-items':
      return generateTopPageItems('new')
    case 'public/get-recommended-auction-items':
      return generateTopPageItems('recommended')
    default:
      return {
        status: 'success',
        message: 'Mock response for development',
      }
  }
}
