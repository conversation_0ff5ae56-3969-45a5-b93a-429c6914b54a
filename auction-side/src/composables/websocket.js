import {ref} from 'vue'
import {w3cwebsocket} from 'websocket'
const W3CWebSocket = w3cwebsocket
const ws = ref(null)
const updatedItems = ref([])
const isConnected = ref(false)
const VITE_WEB_SOCKET_ENDPOINT = import.meta.env.VITE_WEB_SOCKET_ENDPOINT

export default (searchStore, route) => {
  const updateChangedItems = changedItems => {
    changedItems.forEach(changed => {
      searchStore.setItemBySocket(changed, route)
    })
  }
  const connectWs = token => {
    ws.value = new W3CWebSocket(VITE_WEB_SOCKET_ENDPOINT + token)
    ws.value.onopen = event => {
      console.log(event)
      console.log('🔗WebSocket Client Connected')
      isConnected.value = true
    }
    ws.value.onclose = event => {
      isConnected.value = false
      console.log('🔗The connection has been closed successfully.')
      console.log('🔗event.reason', event.reason)
      if (event.reason) {
        console.log('🔗reconnect to wss')
        connectWs(token)
      }
    }
    ws.value.onmessage = e => {
      if (typeof e.data === 'string') {
        const res = JSON.parse(e.data)
        console.log('🔗👍websocket response: ', {res})
        if (res.name === 'changed-items') {
          updateChangedItems(res.items)
        }
      }
    }
  }

  return {ws, updatedItems, connectWs, updateChangedItems}
}
