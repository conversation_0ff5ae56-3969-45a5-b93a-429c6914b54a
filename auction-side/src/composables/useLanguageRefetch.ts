import {useLanguageStore} from '@/stores/language'
import {watch} from 'vue'

export function useLanguageRefetch() {
  const languageStore = useLanguageStore()

  const refetchOnLanguageChange = (fetchFunction: () => Promise<void>) => {
    return watch(
      () => languageStore.currentLanguage,
      async (newLang, oldLang) => {
        if (newLang !== oldLang) {
          console.log(`Language changed from ${oldLang} to ${newLang}, refetching...`)

          try {
            await fetchFunction()
          } catch (error) {
            console.error('Error refetching data on language change:', error)
          }
        }
      }
    )
  }

  return {
    refetchOnLanguageChange,
  }
}
