import {useCognitoAuthStore} from '@/stores/cognitoAuth'
import {computed, ref} from 'vue'
import {useLocale} from 'vuetify'
import {priceMaxLength} from '../defined/const'
import {useBidConfirmStore} from '../stores/bidConfirm'
import {useMessageDialogStore} from '../stores/messag-dialog'
import {format, localeString2Number, priceLocaleString} from './common'
import useSearchProducts from './searchProducts'

/**
 * 入札関連の処理
 */
export default function useBid() {
  const {t: translate} = useLocale()
  const dialog = useMessageDialogStore()
  const bidConfirmStore = useBidConfirmStore()
  const auth = useCognitoAuthStore()

  const bidQuantity = ref('')
  const bidPrice = ref('')

  const bidTotalPrice = computed(() => {
    if (bidQuantity.value && bidPrice.value) {
      return priceLocaleString(
        localeString2Number(bidQuantity.value) * localeString2Number(bidPrice.value)
      )
    }
    return '0'
  })

  // const 1 = pitchWidth => {
  //   // いつも00を表示する
  //   return '00'
  // }

  // removes the padding zeros ("00") from the end of the bid price's localized string representation.
  // const getBidValueSubtractPitch = (price, pitchWidth) => {
  //   const priceLocale = price.toLocaleString()
  //   const pitchComma = getPaddingZero(pitchWidth)
  //   return priceLocale.substring(0, priceLocale.length - pitchComma.length)
  // }

  /**
   * Adjusts the bid price by adding a pitch value and formats it for display.
   * @param {number} currentPrice - The current bid price.
   * @param {number} pitch - The amount to increase the bid price.
   * @param {number} pitchWidth - pitchWidth is fixed by [1] in GEO
   */
  const addPitch = (currentPrice, pitch, pitchWidth) => {
    // todo : pitchWidth
    console.log('pitchWidth: ', pitchWidth)
    if (bidPrice.value) {
      bidPrice.value = String(localeString2Number(bidPrice.value) + pitch)
    } else {
      bidPrice.value = String(localeString2Number(currentPrice) + pitch)
    }
  }

  // 入札確認モーダルを表示する
  const showBidConfirmDialog = bidList => {
    bidConfirmStore.bidConfirmDialogResetParams()
    bidConfirmStore.data = bidList
    bidConfirmStore.toggleBidConfirmDialog()
  }

  const refreshItems = async ({refreshList = false, searchParams = {category: null}}) => {
    if (auth.isAuthenticated) {
      if (refreshList) {
        const {search} = useSearchProducts()
        await search({...searchParams})
      }
    } else {
      dialog.setShowMessage(translate('productDetail.bidModal.loginRequiredMessage'), {
        showOkButton: true,
        showCloseButton: false,
      })
    }
  }

  // Validate bid price and quantity
  const validateBidPrice = ({
    currentBidPrice,
    inputBidPrice,
    inputBidQuantity,
    lowestBidPrice,
    enteredBidPrice,
    enteredBidQuantity,
    isAscendingAuction,
    hasUserBid,
  }) => {
    // if the price is empty and quantity is empty and the entered price is empty, return no error
    if (!enteredBidQuantity && !enteredBidPrice && !inputBidPrice && !inputBidQuantity) {
      return ''
    }
    if (!inputBidPrice) {
      return translate('productDetail.errorMessages.priceEmpty')
    }
    // ロケールごとの桁区切り文字（カンマ、ピリオド、スペースなど）をすべて除去する
    // 対応例：en-US（,）、de-DE（.）、fr-FR（ナローノーブレークスペース）など
    const numericValue = inputBidPrice.replace(/[,.\s\u00A0\u2009\u200A\u202F\u205F\u3000]/g, '')
    // 入力が数値かどうかを確認
    if (!/^\d+$/.test(numericValue)) {
      return translate('productDetail.errorMessages.priceInvalidFormat')
    }
    if (numericValue.length > priceMaxLength) {
      return format(translate('productDetail.errorMessages.priceMaxLength'), [priceMaxLength])
    }
    if (
      typeof lowestBidPrice !== 'undefined' &&
      localeString2Number(numericValue) < lowestBidPrice
    ) {
      if (isAscendingAuction) {
        return translate('productDetail.errorMessages.newPriceLowerThanCurrentPriceErr')
      }
      return translate('productDetail.errorMessages.lowestUnitPriceInvalid')
    }
    // 「競り上げ」かつ「すでに入札」かつ「価格を同じか下げようとしている」の場合エラー
    if (
      isAscendingAuction &&
      hasUserBid &&
      localeString2Number(inputBidPrice) <= localeString2Number(currentBidPrice)
    ) {
      return translate('productDetail.errorMessages.newPriceLowerThanCurrentPriceErr')
    }

    return ''
  }

  const validateBidQuantity = ({
    inputBidPrice,
    inputBidQuantity,
    lowestBidQuantity,
    enteredBidPrice,
    enteredBidQuantity,
    maxQuantity,
  }) => {
    // if the quantity is empty and the entered quantity is empty, return no error
    if (!enteredBidPrice && !enteredBidQuantity && !inputBidQuantity && !inputBidPrice) {
      return ''
    }
    if (!inputBidQuantity) {
      return translate('productDetail.errorMessages.quantityEmpty')
    }
    // カンマを削除
    // Validate the bid quantity
    const quantity = inputBidQuantity.replace(/[,.\s\u00A0\u2009\u200A\u202F\u205F\u3000]/g, '')
    if (!/^\d+$/.test(quantity)) {
      return translate('productDetail.errorMessages.quantityInvalidFormat')
    }
    if (quantity.length > priceMaxLength) {
      return format(translate('productDetail.errorMessages.quantityMaxLength'), [priceMaxLength])
    }
    if (
      typeof lowestBidQuantity !== 'undefined' &&
      localeString2Number(quantity) < lowestBidQuantity
    ) {
      return translate('productDetail.errorMessages.lowestBidQuantityInvalid')
    }
    if (typeof enteredBidPrice !== 'undefined' && localeString2Number(quantity) > maxQuantity) {
      return translate('productDetail.errorMessages.bidQuantityExceedsMax')
    }

    return ''
  }

  const validateBidInput = params => {
    const bidPriceErr = validateBidPrice({
      currentBidPrice: params.currentBidPrice,
      inputBidPrice: params.inputBidPrice,
      inputBidQuantity: params.inputBidQuantity,
      lowestBidPrice: params.lowestBidPrice,
      enteredBidPrice: params.enteredBidPrice,
      enteredBidQuantity: params.enteredBidQuantity,
      isAscendingAuction: params.isAscendingAuction,
      hasUserBid: params.hasUserBid,
    })
    let bidQuantityErr = ''
    if (!params.isAscendingAuction && params.bidQuantity) {
      bidQuantityErr = validateBidQuantity({
        inputBidPrice: params.inputBidPrice,
        inputBidQuantity: params.inputBidQuantity,
        lowestBidQuantity: params.lowestBidQuantity,
        enteredBidPrice: params.enteredBidPrice,
        enteredBidQuantity: params.enteredBidQuantity,
        maxQuantity: params.maxQuantity,
      })
    }
    return {bidPriceErr, bidQuantityErr}
  }

  // 「入札する」ボタンクリックした際に処理
  const bidHandle = params => {
    const {
      exhibitionNo,
      exhibitionItemNo,
      exhibitionName,
      lowestBidPrice,
      lowestBidQuantity,
      pitchWidth,
      freeField,
      newBidPrice,
      newBidQuantity,
      currentBidPrice,
      enteredBidPrice,
      enteredBidQuantity,
      maxQuantity,
      isAscendingAuction,
      hasUserBid,
    } = params

    if (!auth.isAuthenticated) {
      dialog.setShowMessage('ログインが必要です。', {
        showOkButton: true,
        showCloseButton: false,
      })
      return
    }

    // Validate the bid price and bid quantity
    const validate = validateBidInput({
      inputBidPrice: newBidPrice,
      inputBidQuantity: newBidQuantity,
      currentBidPrice,
      enteredBidPrice,
      enteredBidQuantity,
      lowestBidPrice,
      lowestBidQuantity,
      pitchWidth,
      maxQuantity,
      isAscendingAuction,
      hasUserBid,
    })

    if (validate.bidPriceErr || validate.bidQuantityErr) {
      console.log(
        '%c 🇰🇷: validate.bidPriceErr ',
        'font-size:16px;background-color:#72de9d;color:black;',
        validate.bidPriceErr
      )
      dialog.setShowMessage(validate.bidPriceErr || validate.bidQuantityErr, {isErr: true})
      return
    }
    // Show the bid confirmation dialog
    console.log('show bid confirm ')
    showBidConfirmDialog([
      {
        exhibitionNo,
        exhibitionName,
        bidTotalPrice: localeString2Number(bidTotalPrice.value),
        bidList: [
          {
            exhibitionItemNo,
            freeField,
            bidQuantity: localeString2Number(bidQuantity.value),
            bidPrice: localeString2Number(bidPrice.value),
            bidTotalPrice: localeString2Number(bidTotalPrice.value),
          },
        ],
      },
    ])
  }

  const clearBidInput = () => {
    bidPrice.value = ''
    bidQuantity.value = ''
  }

  return {
    bidQuantity,
    bidPrice,
    bidTotalPrice,
    priceMaxLength,
    showBidConfirmDialog,
    addPitch,
    refreshItems,
    validateBidPrice,
    validateBidQuantity,
    validateBidInput,
    clearBidInput,
    bidHandle,
  }
}
