import {Amplify} from 'aws-amplify'

// Use auction-specific Cognito User Pool environment variables
const userPoolId = import.meta.env.VITE_AUCTION_USER_POOL_ID
const clientId = import.meta.env.VITE_AUCTION_CLIENT_ID

if (userPoolId && clientId) {
  Amplify.configure({
    Auth: {
      Cognito: {
        userPoolClientId: clientId,
        userPoolId: userPoolId,
        region: 'ap-northeast-1',
        mandatorySignIn: true,
        loginWith: {
          username: true,
          email: true,
          phone: false,
        },
      },
    },
  })
} else {
  console.error('❌ Missing required auction Cognito configuration:', {
    userPoolId,
    clientId,
    availableEnvVars: Object.keys(import.meta.env).filter(key => key.startsWith('VITE_')),
    mode: import.meta.env.MODE,
  })

  // Throw an error to make the issue more visible
  throw new Error(`Cognito configuration missing. UserPoolId: ${userPoolId}, ClientId: ${clientId}`)
}

export default Amplify
